import { useState } from 'react'
import { Routes, Route, Link, useLocation } from 'react-router-dom'
import { useQuery } from 'react-query'
import api from '../../services/api'
import { PlusIcon, EyeIcon, PencilIcon } from '@heroicons/react/24/outline'

function ProjectsList() {
  const [statusFilter, setStatusFilter] = useState('')
  
  const { data: projects, isLoading } = useQuery(
    ['projects', statusFilter],
    () => api.get('/projects', { 
      params: statusFilter ? { status: statusFilter } : {} 
    }).then(res => res.data.data)
  )

  const statusOptions = [
    { value: '', label: 'جميع المشاريع' },
    { value: 'planning', label: 'تخطيط' },
    { value: 'active', label: 'نشط' },
    { value: 'on_hold', label: 'متوقف' },
    { value: 'completed', label: 'مكتمل' },
    { value: 'cancelled', label: 'ملغي' }
  ]

  const getStatusBadge = (status) => {
    const statusMap = {
      planning: { label: 'تخطيط', class: 'badge-warning' },
      active: { label: 'نشط', class: 'badge-success' },
      on_hold: { label: 'متوقف', class: 'badge-secondary' },
      completed: { label: 'مكتمل', class: 'badge-info' },
      cancelled: { label: 'ملغي', class: 'badge-danger' }
    }
    const statusInfo = statusMap[status] || { label: status, class: 'badge-secondary' }
    return <span className={`badge ${statusInfo.class}`}>{statusInfo.label}</span>
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount || 0)
  }

  if (isLoading) {
    return <div className="flex justify-center py-8"><div className="spinner h-8 w-8"></div></div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">قائمة المشاريع</h2>
        <button className="btn btn-primary">
          <PlusIcon className="h-5 w-5 ml-2" />
          مشروع جديد
        </button>
      </div>

      {/* Filters */}
      <div className="flex space-x-4 space-x-reverse">
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="form-input w-48"
        >
          {statusOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {projects?.map((project) => (
          <div key={project.id} className="card">
            <div className="card-header">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{project.project_name}</h3>
                  <p className="text-sm text-gray-500">{project.project_code}</p>
                </div>
                {getStatusBadge(project.status)}
              </div>
            </div>
            
            <div className="card-body">
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">العميل</p>
                  <p className="text-sm text-gray-900">{project.client_name}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-700">نوع المشروع</p>
                  <p className="text-sm text-gray-900">
                    {project.project_type === 'civil' ? 'مدني' :
                     project.project_type === 'structural' ? 'إنشائي' :
                     project.project_type === 'finishing' ? 'تشطيبات' :
                     project.project_type === 'electrical' ? 'كهرباء' :
                     project.project_type === 'plumbing' ? 'سباكة' :
                     project.project_type === 'infrastructure' ? 'بنية تحتية' :
                     project.project_type}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700">الميزانية المقدرة</p>
                    <p className="text-sm text-gray-900">{formatCurrency(project.estimated_budget)}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">التكلفة الفعلية</p>
                    <p className="text-sm text-gray-900">{formatCurrency(project.actual_cost)}</p>
                  </div>
                </div>

                {project.start_date && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">تاريخ البداية</p>
                    <p className="text-sm text-gray-900">
                      {new Date(project.start_date).toLocaleDateString('ar-EG')}
                    </p>
                  </div>
                )}

                {project.project_manager_name && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">مدير المشروع</p>
                    <p className="text-sm text-gray-900">{project.project_manager_name}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="card-footer">
              <div className="flex justify-between">
                <button className="btn btn-sm btn-outline">
                  <EyeIcon className="h-4 w-4 ml-1" />
                  عرض
                </button>
                <button className="btn btn-sm btn-outline">
                  <PencilIcon className="h-4 w-4 ml-1" />
                  تعديل
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {projects && projects.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">لا توجد مشاريع</p>
        </div>
      )}
    </div>
  )
}

function ProjectDetails() {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">تفاصيل المشروع</h2>
      <div className="card">
        <div className="card-body">
          <p className="text-gray-500">تفاصيل المشروع ستظهر هنا</p>
        </div>
      </div>
    </div>
  )
}

function ProjectsPage() {
  const location = useLocation()
  
  const tabs = [
    { name: 'قائمة المشاريع', path: '/projects', component: ProjectsList },
    { name: 'تفاصيل المشروع', path: '/projects/details', component: ProjectDetails }
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">إدارة المشاريع</h1>
        <p className="mt-1 text-sm text-gray-500">
          إدارة المشاريع وتتبع التكاليف والميزانيات
        </p>
      </div>

      {/* Tabs Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 space-x-reverse">
          {tabs.map((tab) => {
            const isActive = location.pathname === tab.path
            return (
              <Link
                key={tab.name}
                to={tab.path}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  isActive
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </Link>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <Routes>
        <Route path="/" element={<ProjectsList />} />
        <Route path="/details" element={<ProjectDetails />} />
      </Routes>
    </div>
  )
}

export default ProjectsPage
