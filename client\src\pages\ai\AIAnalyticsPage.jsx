import { useQuery } from 'react-query'
import api from '../../services/api'
import { 
  ChartBarIcon, 
  ExclamationTriangleIcon,
  LightBulbIcon,
  TrendingUpIcon 
} from '@heroicons/react/24/outline'

function AIAnalyticsPage() {
  const { data: predictions, isLoading: loadingPredictions } = useQuery(
    'ai-predictions',
    () => api.get('/ai/predictions').then(res => res.data.data)
  )

  const { data: recommendations, isLoading: loadingRecommendations } = useQuery(
    'ai-recommendations',
    () => api.get('/ai/recommendations').then(res => res.data.data)
  )

  const getPredictionIcon = (type) => {
    switch (type) {
      case 'cash_flow': return '💰'
      case 'cost_overrun': return '📈'
      case 'payment_delay': return '⏰'
      case 'resource_optimization': return '⚡'
      default: return '🔮'
    }
  }

  const getPredictionTitle = (type) => {
    switch (type) {
      case 'cash_flow': return 'توقع التدفق النقدي'
      case 'cost_overrun': return 'توقع تجاوز التكلفة'
      case 'payment_delay': return 'توقع تأخير الدفع'
      case 'resource_optimization': return 'تحسين الموارد'
      default: return type
    }
  }

  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'cost_reduction': return '💰'
      case 'process_improvement': return '⚙️'
      case 'risk_mitigation': return '🛡️'
      case 'opportunity': return '🎯'
      default: return '💡'
    }
  }

  const getRecommendationTitle = (type) => {
    switch (type) {
      case 'cost_reduction': return 'تقليل التكاليف'
      case 'process_improvement': return 'تحسين العمليات'
      case 'risk_mitigation': return 'تخفيف المخاطر'
      case 'opportunity': return 'فرصة'
      default: return type
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">التحليلات الذكية</h1>
        <p className="mt-1 text-sm text-gray-500">
          تحليلات وتوقعات مدعومة بالذكاء الاصطناعي
        </p>
      </div>

      {/* AI Insights Overview */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-lg bg-blue-100">
                  <ChartBarIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mr-4 flex-1">
                <p className="text-sm font-medium text-gray-500">التوقعات النشطة</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {predictions?.length || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-lg bg-yellow-100">
                  <LightBulbIcon className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div className="mr-4 flex-1">
                <p className="text-sm font-medium text-gray-500">التوصيات</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {recommendations?.length || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-lg bg-red-100">
                  <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                </div>
              </div>
              <div className="mr-4 flex-1">
                <p className="text-sm font-medium text-gray-500">تحذيرات عالية</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {recommendations?.filter(r => r.priority === 'high' || r.priority === 'critical').length || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-lg bg-green-100">
                  <TrendingUpIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="mr-4 flex-1">
                <p className="text-sm font-medium text-gray-500">دقة التوقعات</p>
                <p className="text-2xl font-semibold text-gray-900">87%</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI Predictions */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">التوقعات الذكية</h3>
          </div>
          <div className="card-body">
            {loadingPredictions ? (
              <div className="flex justify-center py-4">
                <div className="spinner h-6 w-6"></div>
              </div>
            ) : predictions && predictions.length > 0 ? (
              <div className="space-y-4">
                {predictions.slice(0, 5).map((prediction) => (
                  <div key={prediction.id} className="flex items-start space-x-3 space-x-reverse">
                    <div className="flex-shrink-0">
                      <div className="text-2xl">{getPredictionIcon(prediction.prediction_type)}</div>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {getPredictionTitle(prediction.prediction_type)}
                      </p>
                      <p className="text-sm text-gray-500">
                        تاريخ التوقع: {new Date(prediction.prediction_date).toLocaleDateString('ar-EG')}
                      </p>
                      {prediction.confidence_score && (
                        <div className="mt-1">
                          <span className="text-xs text-gray-500">دقة التوقع: </span>
                          <span className="text-xs font-medium text-blue-600">
                            {(prediction.confidence_score * 100).toFixed(0)}%
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">لا توجد توقعات حالياً</p>
            )}
          </div>
        </div>

        {/* AI Recommendations */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">التوصيات الذكية</h3>
          </div>
          <div className="card-body">
            {loadingRecommendations ? (
              <div className="flex justify-center py-4">
                <div className="spinner h-6 w-6"></div>
              </div>
            ) : recommendations && recommendations.length > 0 ? (
              <div className="space-y-4">
                {recommendations.slice(0, 5).map((recommendation) => (
                  <div key={recommendation.id} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-start space-x-3 space-x-reverse">
                      <div className="flex-shrink-0">
                        <div className="text-xl">{getRecommendationIcon(recommendation.recommendation_type)}</div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900">{recommendation.title}</p>
                          <span className={`badge ${
                            recommendation.priority === 'critical' ? 'badge-danger' :
                            recommendation.priority === 'high' ? 'badge-warning' :
                            recommendation.priority === 'medium' ? 'badge-info' :
                            'badge-secondary'
                          }`}>
                            {recommendation.priority === 'critical' ? 'حرج' :
                             recommendation.priority === 'high' ? 'عالي' :
                             recommendation.priority === 'medium' ? 'متوسط' :
                             'منخفض'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">{recommendation.description}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-gray-500">
                            {getRecommendationTitle(recommendation.recommendation_type)}
                          </span>
                          {recommendation.estimated_impact && (
                            <span className="text-xs font-medium text-green-600">
                              توفير متوقع: {recommendation.estimated_impact.toLocaleString('ar-EG')} ج.م
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">لا توجد توصيات حالياً</p>
            )}
          </div>
        </div>
      </div>

      {/* AI Features */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">ميزات الذكاء الاصطناعي</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-3xl mb-2">📊</div>
              <h4 className="font-medium text-gray-900">تحليل التدفق النقدي</h4>
              <p className="text-sm text-gray-500 mt-1">توقع التدفقات النقدية المستقبلية</p>
            </div>
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-3xl mb-2">⚠️</div>
              <h4 className="font-medium text-gray-900">كشف المخاطر</h4>
              <p className="text-sm text-gray-500 mt-1">تحديد المخاطر المالية مبكراً</p>
            </div>
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-3xl mb-2">🎯</div>
              <h4 className="font-medium text-gray-900">تحسين التكاليف</h4>
              <p className="text-sm text-gray-500 mt-1">اقتراحات لتقليل التكاليف</p>
            </div>
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-3xl mb-2">📈</div>
              <h4 className="font-medium text-gray-900">تحليل الأداء</h4>
              <p className="text-sm text-gray-500 mt-1">مراقبة أداء المشاريع والشركة</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AIAnalyticsPage
