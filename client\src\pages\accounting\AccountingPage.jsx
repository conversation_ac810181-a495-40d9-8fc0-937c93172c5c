import { Routes, Route, Link, useLocation } from 'react-router-dom'
import { useQuery } from 'react-query'
import api from '../../services/api'

// Sub-components
function ChartOfAccounts() {
  const { data: accounts, isLoading } = useQuery(
    'chart-of-accounts',
    () => api.get('/accounting/chart-of-accounts').then(res => res.data.data)
  )

  if (isLoading) {
    return <div className="flex justify-center py-8"><div className="spinner h-8 w-8"></div></div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">دليل الحسابات</h2>
        <button className="btn btn-primary">إضافة حساب جديد</button>
      </div>

      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>رمز الحساب</th>
                <th>اسم الحساب</th>
                <th>نوع الحساب</th>
                <th>الحساب الأب</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {accounts?.map((account) => (
                <tr key={account.id}>
                  <td className="font-mono">{account.account_code}</td>
                  <td>{account.account_name}</td>
                  <td>
                    <span className="badge badge-info">
                      {account.account_type === 'asset' ? 'أصول' :
                       account.account_type === 'liability' ? 'خصوم' :
                       account.account_type === 'equity' ? 'حقوق ملكية' :
                       account.account_type === 'revenue' ? 'إيرادات' :
                       account.account_type === 'expense' ? 'مصروفات' :
                       account.account_type}
                    </span>
                  </td>
                  <td>{account.parent_account_name || '-'}</td>
                  <td>
                    <span className={`badge ${account.is_active ? 'badge-success' : 'badge-secondary'}`}>
                      {account.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td>
                    <button className="btn btn-sm btn-outline ml-2">تعديل</button>
                    <button className="btn btn-sm btn-danger">حذف</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

function GeneralLedger() {
  const { data: ledgerData, isLoading } = useQuery(
    'general-ledger',
    () => api.get('/accounting/general-ledger?limit=50').then(res => res.data.data)
  )

  if (isLoading) {
    return <div className="flex justify-center py-8"><div className="spinner h-8 w-8"></div></div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">دفتر الأستاذ العام</h2>
        <button className="btn btn-primary">إضافة قيد جديد</button>
      </div>

      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>رقم القيد</th>
                <th>التاريخ</th>
                <th>الحساب</th>
                <th>البيان</th>
                <th>مدين</th>
                <th>دائن</th>
                <th>المشروع</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {ledgerData?.entries?.map((entry) => (
                <tr key={entry.id}>
                  <td className="font-mono">{entry.entry_number}</td>
                  <td>{new Date(entry.transaction_date).toLocaleDateString('ar-EG')}</td>
                  <td>
                    <div>
                      <div className="font-medium">{entry.account_name}</div>
                      <div className="text-sm text-gray-500">{entry.account_code}</div>
                    </div>
                  </td>
                  <td>{entry.description}</td>
                  <td className="text-green-600 font-medium">
                    {entry.debit_amount > 0 ? entry.debit_amount.toLocaleString('ar-EG') : '-'}
                  </td>
                  <td className="text-red-600 font-medium">
                    {entry.credit_amount > 0 ? entry.credit_amount.toLocaleString('ar-EG') : '-'}
                  </td>
                  <td>{entry.project_name || '-'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

function TrialBalance() {
  const { data: trialBalance, isLoading } = useQuery(
    'trial-balance',
    () => api.get('/accounting/trial-balance').then(res => res.data.data)
  )

  if (isLoading) {
    return <div className="flex justify-center py-8"><div className="spinner h-8 w-8"></div></div>
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">ميزان المراجعة</h2>
        <div className="text-sm text-gray-500">
          كما في: {new Date(trialBalance?.asOfDate).toLocaleDateString('ar-EG')}
        </div>
      </div>

      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>رمز الحساب</th>
                <th>اسم الحساب</th>
                <th>نوع الحساب</th>
                <th>إجمالي المدين</th>
                <th>إجمالي الدائن</th>
                <th>الرصيد</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {trialBalance?.accounts?.map((account) => (
                <tr key={account.id}>
                  <td className="font-mono">{account.account_code}</td>
                  <td>{account.account_name}</td>
                  <td>
                    <span className="badge badge-info">
                      {account.account_type === 'asset' ? 'أصول' :
                       account.account_type === 'liability' ? 'خصوم' :
                       account.account_type === 'equity' ? 'حقوق ملكية' :
                       account.account_type === 'revenue' ? 'إيرادات' :
                       account.account_type === 'expense' ? 'مصروفات' :
                       account.account_type}
                    </span>
                  </td>
                  <td className="text-green-600">{formatCurrency(account.total_debits)}</td>
                  <td className="text-red-600">{formatCurrency(account.total_credits)}</td>
                  <td className={`font-medium ${account.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(Math.abs(account.balance))}
                    {account.balance < 0 && ' (دائن)'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {trialBalance?.totals && (
          <div className="card-footer">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                إجمالي المدين: {formatCurrency(trialBalance.totals.totalDebits)}
              </div>
              <div className="text-sm text-gray-600">
                إجمالي الدائن: {formatCurrency(trialBalance.totals.totalCredits)}
              </div>
              <div className={`text-sm font-medium ${
                trialBalance.totals.isBalanced ? 'text-green-600' : 'text-red-600'
              }`}>
                {trialBalance.totals.isBalanced ? 'متوازن ✓' : 'غير متوازن ✗'}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

function AccountingPage() {
  const location = useLocation()
  
  const tabs = [
    { name: 'دليل الحسابات', path: '/accounting', component: ChartOfAccounts },
    { name: 'دفتر الأستاذ العام', path: '/accounting/general-ledger', component: GeneralLedger },
    { name: 'ميزان المراجعة', path: '/accounting/trial-balance', component: TrialBalance }
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">المحاسبة العامة</h1>
        <p className="mt-1 text-sm text-gray-500">
          إدارة دليل الحسابات والقيود المحاسبية
        </p>
      </div>

      {/* Tabs Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 space-x-reverse">
          {tabs.map((tab) => {
            const isActive = location.pathname === tab.path
            return (
              <Link
                key={tab.name}
                to={tab.path}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  isActive
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </Link>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <Routes>
        <Route path="/" element={<ChartOfAccounts />} />
        <Route path="/general-ledger" element={<GeneralLedger />} />
        <Route path="/trial-balance" element={<TrialBalance />} />
      </Routes>
    </div>
  )
}

export default AccountingPage
