import { useQuery } from 'react-query'
import api from '../../services/api'

function InventoryPage() {
  const { data: items, isLoading } = useQuery(
    'inventory-items',
    () => api.get('/inventory').then(res => res.data.data)
  )

  if (isLoading) {
    return <div className="flex justify-center py-8"><div className="spinner h-8 w-8"></div></div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة المخزون</h1>
          <p className="mt-1 text-sm text-gray-500">إدارة المواد والأصناف</p>
        </div>
        <button className="btn btn-primary">إضافة صنف جديد</button>
      </div>

      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>رمز الصنف</th>
                <th>اسم الصنف</th>
                <th>الفئة</th>
                <th>وحدة القياس</th>
                <th>الرصيد الحالي</th>
                <th>الحد الأدنى</th>
                <th>سعر الوحدة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {items?.map((item) => (
                <tr key={item.id}>
                  <td className="font-mono">{item.item_code}</td>
                  <td>{item.item_name}</td>
                  <td>{item.category}</td>
                  <td>{item.unit_of_measure}</td>
                  <td className={item.current_stock <= item.minimum_stock ? 'text-red-600 font-medium' : ''}>
                    {item.current_stock}
                  </td>
                  <td>{item.minimum_stock}</td>
                  <td>{item.unit_cost?.toLocaleString('ar-EG')} ج.م</td>
                  <td>
                    <button className="btn btn-sm btn-outline ml-2">تعديل</button>
                    <button className="btn btn-sm btn-secondary">حركة</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default InventoryPage
