import { forwardRef } from 'react'

const EnhancedCard = forwardRef(({ 
  children, 
  className = '', 
  clickable = false, 
  hover = true,
  padding = 'p-6',
  onClick,
  ...props 
}, ref) => {
  const baseClasses = `
    enhanced-card
    ${padding}
    ${clickable ? 'clickable cursor-pointer' : ''}
    ${hover ? 'hover:shadow-md hover:-translate-y-1' : ''}
    transition-all duration-200
    ${className}
  `

  return (
    <div
      ref={ref}
      className={baseClasses.trim()}
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  )
})

EnhancedCard.displayName = 'EnhancedCard'

export default EnhancedCard
