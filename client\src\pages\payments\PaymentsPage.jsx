import { useQuery } from 'react-query'
import api from '../../services/api'

function PaymentsPage() {
  const { data: payments, isLoading } = useQuery(
    'payments',
    () => api.get('/payments').then(res => res.data.data)
  )

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount || 0)
  }

  if (isLoading) {
    return <div className="flex justify-center py-8"><div className="spinner h-8 w-8"></div></div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة المدفوعات</h1>
          <p className="mt-1 text-sm text-gray-500">المقبوضات والمدفوعات</p>
        </div>
        <button className="btn btn-primary">تسجيل دفعة جديدة</button>
      </div>

      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>رقم الدفعة</th>
                <th>النوع</th>
                <th>الطرف</th>
                <th>المبلغ</th>
                <th>طريقة الدفع</th>
                <th>تاريخ الدفع</th>
                <th>الحساب البنكي</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {payments?.map((payment) => (
                <tr key={payment.id}>
                  <td className="font-mono">{payment.payment_number}</td>
                  <td>
                    <span className={`badge ${payment.payment_type === 'receipt' ? 'badge-success' : 'badge-info'}`}>
                      {payment.payment_type === 'receipt' ? 'مقبوض' : 'مدفوع'}
                    </span>
                  </td>
                  <td>{payment.party_name}</td>
                  <td>{formatCurrency(payment.amount)}</td>
                  <td>
                    {payment.payment_method === 'cash' ? 'نقدي' :
                     payment.payment_method === 'bank_transfer' ? 'تحويل بنكي' :
                     payment.payment_method === 'check' ? 'شيك' :
                     payment.payment_method === 'credit_card' ? 'بطاقة ائتمان' :
                     payment.payment_method}
                  </td>
                  <td>{new Date(payment.payment_date).toLocaleDateString('ar-EG')}</td>
                  <td>{payment.bank_account_name || '-'}</td>
                  <td>
                    <button className="btn btn-sm btn-outline ml-2">عرض</button>
                    <button className="btn btn-sm btn-secondary">طباعة</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default PaymentsPage
