import { useState } from 'react'
import { useQuery } from 'react-query'
import api from '../../services/api'
import { DocumentArrowDownIcon } from '@heroicons/react/24/outline'

function ReportsPage() {
  const [reportPeriod, setReportPeriod] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  })

  const { data: financialSummary, isLoading } = useQuery(
    ['financial-summary', reportPeriod],
    () => api.get('/reports/financial-summary', { params: reportPeriod }).then(res => res.data.data),
    { enabled: !!reportPeriod.startDate && !!reportPeriod.endDate }
  )

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount || 0)
  }

  const reports = [
    { name: 'قائمة الدخل', description: 'تقرير الإيرادات والمصروفات', icon: '📊' },
    { name: 'الميزانية العمومية', description: 'الأصول والخصوم وحقوق الملكية', icon: '⚖️' },
    { name: 'تقرير التدفق النقدي', description: 'حركة النقدية الداخلة والخارجة', icon: '💰' },
    { name: 'تقرير المشاريع', description: 'أداء وتكاليف المشاريع', icon: '🏗️' },
    { name: 'تقرير المخزون', description: 'حالة المخزون والحركات', icon: '📦' },
    { name: 'تقرير الضرائب', description: 'ملخص الضرائب والامتثال', icon: '🧾' },
    { name: 'تقرير الموارد البشرية', description: 'الرواتب والحضور', icon: '👥' },
    { name: 'تقرير العملاء', description: 'أرصدة وحركات العملاء', icon: '🤝' }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">التقارير</h1>
        <p className="mt-1 text-sm text-gray-500">
          تقارير مالية ومحاسبية شاملة
        </p>
      </div>

      {/* Period Selection */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">فترة التقرير</h3>
        </div>
        <div className="card-body">
          <div className="flex space-x-4 space-x-reverse">
            <div>
              <label className="form-label">من تاريخ</label>
              <input
                type="date"
                value={reportPeriod.startDate}
                onChange={(e) => setReportPeriod(prev => ({ ...prev, startDate: e.target.value }))}
                className="form-input"
              />
            </div>
            <div>
              <label className="form-label">إلى تاريخ</label>
              <input
                type="date"
                value={reportPeriod.endDate}
                onChange={(e) => setReportPeriod(prev => ({ ...prev, endDate: e.target.value }))}
                className="form-input"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Financial Summary */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">الملخص المالي</h3>
        </div>
        <div className="card-body">
          {isLoading ? (
            <div className="flex justify-center py-4">
              <div className="spinner h-6 w-6"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900">إجمالي الإيرادات</h4>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(financialSummary?.revenue)}
                </p>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <h4 className="font-medium text-red-900">إجمالي المصروفات</h4>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(financialSummary?.expenses)}
                </p>
              </div>
              <div className={`p-4 rounded-lg ${
                financialSummary?.netIncome >= 0 ? 'bg-blue-50' : 'bg-red-50'
              }`}>
                <h4 className={`font-medium ${
                  financialSummary?.netIncome >= 0 ? 'text-blue-900' : 'text-red-900'
                }`}>
                  صافي الربح/الخسارة
                </h4>
                <p className={`text-2xl font-bold ${
                  financialSummary?.netIncome >= 0 ? 'text-blue-600' : 'text-red-600'
                }`}>
                  {formatCurrency(financialSummary?.netIncome)}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Available Reports */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">التقارير المتاحة</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {reports.map((report, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="text-center">
                  <div className="text-3xl mb-2">{report.icon}</div>
                  <h4 className="font-medium text-gray-900 mb-1">{report.name}</h4>
                  <p className="text-sm text-gray-500 mb-3">{report.description}</p>
                  <button className="btn btn-sm btn-outline w-full">
                    <DocumentArrowDownIcon className="h-4 w-4 ml-1" />
                    تصدير
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReportsPage
