import { useQuery } from 'react-query'
import api from '../services/api'
import {
  ChartBarIcon,
  BriefcaseIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

function DashboardPage() {
  // Fetch dashboard data
  const { data: financialSummary, isLoading: loadingFinancial } = useQuery(
    'financial-summary',
    () => api.get('/reports/financial-summary', {
      params: {
        startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0]
      }
    }).then(res => res.data.data),
    { refetchInterval: 300000 } // Refresh every 5 minutes
  )

  const { data: projects } = useQuery(
    'dashboard-projects',
    () => api.get('/projects?limit=5').then(res => res.data.data)
  )

  const { data: aiRecommendations } = useQuery(
    'ai-recommendations',
    () => api.get('/ai/recommendations?limit=5').then(res => res.data.data)
  )

  const stats = [
    {
      name: 'إجمالي الإيرادات',
      value: financialSummary?.revenue || 0,
      icon: CurrencyDollarIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      name: 'إجمالي المصروفات',
      value: financialSummary?.expenses || 0,
      icon: ChartBarIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    },
    {
      name: 'صافي الربح',
      value: financialSummary?.netIncome || 0,
      icon: CurrencyDollarIcon,
      color: financialSummary?.netIncome >= 0 ? 'text-green-600' : 'text-red-600',
      bgColor: financialSummary?.netIncome >= 0 ? 'bg-green-100' : 'bg-red-100'
    },
    {
      name: 'المشاريع النشطة',
      value: projects?.filter(p => p.status === 'active').length || 0,
      icon: BriefcaseIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    }
  ]

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">لوحة التحكم</h1>
        <p className="mt-1 text-sm text-gray-500">
          نظرة عامة على أداء الشركة والمشاريع
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
                <div className="mr-4 flex-1">
                  <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {stat.name.includes('المشاريع') ? stat.value : formatCurrency(stat.value)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Projects */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">المشاريع الحديثة</h3>
          </div>
          <div className="card-body">
            {projects && projects.length > 0 ? (
              <div className="space-y-4">
                {projects.slice(0, 5).map((project) => (
                  <div key={project.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{project.project_name}</p>
                      <p className="text-sm text-gray-500">{project.client_name}</p>
                    </div>
                    <div className="text-left">
                      <span className={`badge ${
                        project.status === 'active' ? 'badge-success' :
                        project.status === 'planning' ? 'badge-warning' :
                        project.status === 'completed' ? 'badge-info' :
                        'badge-secondary'
                      }`}>
                        {project.status === 'active' ? 'نشط' :
                         project.status === 'planning' ? 'تخطيط' :
                         project.status === 'completed' ? 'مكتمل' :
                         project.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">لا توجد مشاريع</p>
            )}
          </div>
        </div>

        {/* AI Recommendations */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">توصيات الذكاء الاصطناعي</h3>
          </div>
          <div className="card-body">
            {aiRecommendations && aiRecommendations.length > 0 ? (
              <div className="space-y-4">
                {aiRecommendations.slice(0, 5).map((recommendation) => (
                  <div key={recommendation.id} className="flex items-start space-x-3 space-x-reverse">
                    <div className="flex-shrink-0">
                      <ExclamationTriangleIcon className={`h-5 w-5 ${
                        recommendation.priority === 'critical' ? 'text-red-500' :
                        recommendation.priority === 'high' ? 'text-orange-500' :
                        recommendation.priority === 'medium' ? 'text-yellow-500' :
                        'text-blue-500'
                      }`} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{recommendation.title}</p>
                      <p className="text-sm text-gray-500">{recommendation.description}</p>
                      <span className={`badge mt-1 ${
                        recommendation.priority === 'critical' ? 'badge-danger' :
                        recommendation.priority === 'high' ? 'badge-warning' :
                        recommendation.priority === 'medium' ? 'badge-info' :
                        'badge-secondary'
                      }`}>
                        {recommendation.priority === 'critical' ? 'حرج' :
                         recommendation.priority === 'high' ? 'عالي' :
                         recommendation.priority === 'medium' ? 'متوسط' :
                         'منخفض'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">لا توجد توصيات حالياً</p>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">إجراءات سريعة</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
            <button className="btn btn-outline">
              إنشاء مشروع جديد
            </button>
            <button className="btn btn-outline">
              إضافة فاتورة
            </button>
            <button className="btn btn-outline">
              تسجيل دفعة
            </button>
            <button className="btn btn-outline">
              عرض التقارير
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
