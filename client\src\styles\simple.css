/* Simple Enhanced Styles */

/* Enhanced Card */
.enhanced-card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  border: 1px solid #f1f5f9;
  transition: all 0.25s ease-in-out;
  overflow: hidden;
}

.enhanced-card:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  transform: translateY(-2px);
  border-color: #e2e8f0;
}

.enhanced-card.clickable {
  cursor: pointer;
}

.enhanced-card.clickable:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  transform: translateY(-4px);
}

/* Enhanced Button */
.btn-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.15s ease-in-out;
  border: none;
  cursor: pointer;
  text-decoration: none;
  gap: 0.5rem;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.btn-secondary {
  background-color: #64748b;
  color: white;
}

.btn-secondary:hover {
  background-color: #475569;
  transform: translateY(-1px);
}

.btn-success {
  background-color: #10b981;
  color: white;
}

.btn-success:hover {
  background-color: #059669;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: #f59e0b;
  color: white;
}

.btn-warning:hover {
  background-color: #d97706;
  transform: translateY(-1px);
}

.btn-error {
  background-color: #ef4444;
  color: white;
}

.btn-error:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  gap: 0.25rem;
}

.status-success {
  background-color: rgb(220 252 231);
  color: rgb(22 101 52);
}

.status-warning {
  background-color: rgb(254 243 199);
  color: rgb(146 64 14);
}

.status-error {
  background-color: rgb(254 226 226);
  color: rgb(153 27 27);
}

.status-info {
  background-color: rgb(219 234 254);
  color: rgb(30 58 138);
}

.status-neutral {
  background-color: rgb(241 245 249);
  color: rgb(51 65 85);
}

/* Progress Bar */
.progress-enhanced {
  width: 100%;
  height: 0.5rem;
  background-color: #f1f5f9;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 9999px;
  transition: width 0.25s ease-in-out;
}

.progress-success {
  background-color: #10b981;
}

.progress-warning {
  background-color: #f59e0b;
}

.progress-error {
  background-color: #ef4444;
}

/* Input Enhanced */
.input-enhanced {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  background-color: white;
  color: #1e293b;
  font-size: 0.875rem;
  transition: all 0.15s ease-in-out;
}

.input-enhanced:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.input-enhanced:hover {
  border-color: #64748b;
}

/* Loading Spinner */
.spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Arabic Support */
body {
  font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  direction: rtl;
  text-align: right;
}

/* Responsive */
@media (max-width: 768px) {
  .enhanced-card {
    margin: 0.5rem;
    border-radius: 0.5rem;
  }
  
  .btn-enhanced {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}
