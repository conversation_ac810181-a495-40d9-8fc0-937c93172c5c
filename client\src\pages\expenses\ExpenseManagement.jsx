import { useState, useEffect } from 'react'
import { EnhancedCard, EnhancedButton, StatusBadge, ProgressBar } from '../../components/ui'

function ExpenseManagement() {
  const [activeTab, setActiveTab] = useState('advances')
  const [autoSettlement, setAutoSettlement] = useState(true)
  const [selectedAdvance, setSelectedAdvance] = useState(null)
  const [newReceipt, setNewReceipt] = useState({
    description: '',
    amount: '',
    date: new Date().toISOString().split('T')[0],
    category: '',
    attachments: []
  })

  // بيانات العهد المحسنة
  const advances = [
    {
      id: 'ADV-001',
      employeeName: 'أحمد محمد علي',
      employeeId: 'EMP-001',
      department: 'مشروع الفيلا السكنية',
      projectCode: 'PRJ-001',
      amount: 5000,
      date: '2024-01-15',
      dueDate: '2024-01-30',
      purpose: 'شراء مواد بناء صغيرة',
      status: 'قيد التسوية',
      priority: 'عادي',
      autoSettlementEnabled: true,
      receipts: [
        {
          id: 'R-001',
          description: 'فاتورة أسمنت من شركة البناء المتحدة',
          amount: 1500,
          date: '2024-01-16',
          category: 'مواد بناء',
          verified: true,
          attachments: ['receipt_001.pdf']
        },
        {
          id: 'R-002',
          description: 'فاتورة حديد تسليح من مصنع الحديد',
          amount: 2000,
          date: '2024-01-17',
          category: 'مواد بناء',
          verified: true,
          attachments: ['receipt_002.pdf']
        }
      ],
      totalReceipts: 3500,
      remainingAmount: 1500,
      settlementPercentage: 70
    },
    {
      id: 'ADV-002',
      employeeName: 'سارة أحمد محمود',
      employeeId: 'EMP-002',
      department: 'المشتريات',
      projectCode: 'GEN-001',
      amount: 3000,
      date: '2024-01-20',
      dueDate: '2024-02-05',
      purpose: 'مصروفات نثرية للمكتب',
      status: 'مسواة',
      priority: 'منخفض',
      autoSettlementEnabled: true,
      receipts: [
        {
          id: 'R-003',
          description: 'قرطاسية ومستلزمات مكتبية',
          amount: 800,
          date: '2024-01-21',
          category: 'مصروفات إدارية',
          verified: true,
          attachments: ['receipt_003.pdf']
        },
        {
          id: 'R-004',
          description: 'وقود سيارة الشركة',
          amount: 1200,
          date: '2024-01-22',
          category: 'مواصلات',
          verified: true,
          attachments: ['receipt_004.pdf']
        },
        {
          id: 'R-005',
          description: 'مصروفات اتصالات وإنترنت',
          amount: 500,
          date: '2024-01-23',
          category: 'اتصالات',
          verified: true,
          attachments: ['receipt_005.pdf']
        },
        {
          id: 'R-006',
          description: 'رد المبلغ المتبقي',
          amount: 500,
          date: '2024-01-24',
          category: 'رد نقدي',
          verified: true,
          attachments: []
        }
      ],
      totalReceipts: 3000,
      remainingAmount: 0,
      settlementPercentage: 100
    },
    {
      id: 'ADV-003',
      employeeName: 'محمد حسن علي',
      employeeId: 'EMP-003',
      department: 'مشروع المجمع التجاري',
      projectCode: 'PRJ-002',
      amount: 8000,
      date: '2024-01-25',
      dueDate: '2024-02-10',
      purpose: 'شراء معدات وأدوات للموقع',
      status: 'متأخر',
      priority: 'عالي',
      autoSettlementEnabled: false,
      receipts: [
        {
          id: 'R-007',
          description: 'أدوات كهربائية متنوعة',
          amount: 2500,
          date: '2024-01-26',
          category: 'معدات',
          verified: false,
          attachments: ['receipt_007.pdf']
        }
      ],
      totalReceipts: 2500,
      remainingAmount: 5500,
      settlementPercentage: 31
    },
    {
      id: 'ADV-004',
      employeeName: 'فاطمة أحمد محمد',
      employeeId: 'EMP-004',
      department: 'المحاسبة',
      projectCode: 'GEN-001',
      amount: 2000,
      date: '2024-01-28',
      dueDate: '2024-02-12',
      purpose: 'مصروفات بنكية ورسوم',
      status: 'جديد',
      priority: 'عادي',
      autoSettlementEnabled: true,
      receipts: [],
      totalReceipts: 0,
      remainingAmount: 2000,
      settlementPercentage: 0
    }
  ]

  // المصروفات العمومية
  const generalExpenses = [
    {
      id: 'EXP-001',
      category: 'إيجارات',
      description: 'إيجار مكتب الإدارة - يناير 2024',
      amount: 8000,
      date: '2024-01-01',
      paymentMethod: 'تحويل بنكي',
      status: 'مدفوع',
      project: 'عام'
    },
    {
      id: 'EXP-002',
      category: 'مرافق',
      description: 'فاتورة كهرباء المكتب',
      amount: 1200,
      date: '2024-01-15',
      paymentMethod: 'نقدي',
      status: 'مدفوع',
      project: 'عام'
    },
    {
      id: 'EXP-003',
      category: 'صيانة',
      description: 'صيانة معدات الموقع',
      amount: 2500,
      date: '2024-01-18',
      paymentMethod: 'شيك',
      status: 'قيد المراجعة',
      project: 'مشروع الفيلا السكنية'
    }
  ]

  const tabs = [
    { id: 'advances', name: 'العهد', icon: '💰' },
    { id: 'auto-settlement', name: 'التسوية التلقائية', icon: '🤖' },
    { id: 'settlement', name: 'تسوية العهد', icon: '📋' },
    { id: 'general-expenses', name: 'المصروفات العمومية', icon: '📊' },
    { id: 'analytics', name: 'تحليلات العهد', icon: '📈' },
    { id: 'reports', name: 'التقارير', icon: '📊' }
  ]

  // وظائف التسوية التلقائية
  const handleAutoSettlement = (advanceId) => {
    const advance = advances.find(a => a.id === advanceId)
    if (!advance) return

    // محاكاة التسوية التلقائية
    const totalReceipts = advance.receipts.reduce((sum, r) => sum + r.amount, 0)
    const remaining = advance.amount - totalReceipts

    if (remaining === 0) {
      alert(`✅ تم تسوية العهدة ${advanceId} تلقائياً!\n\nالمبلغ: ${advance.amount.toLocaleString('ar-EG')} ج.م\nالإيصالات: ${totalReceipts.toLocaleString('ar-EG')} ج.م\nالحالة: مسواة بالكامل`)
    } else if (remaining > 0) {
      alert(`⚠️ العهدة ${advanceId} تحتاج تسوية!\n\nالمبلغ: ${advance.amount.toLocaleString('ar-EG')} ج.م\nالإيصالات: ${totalReceipts.toLocaleString('ar-EG')} ج.م\nالمتبقي: ${remaining.toLocaleString('ar-EG')} ج.م\n\nيرجى إضافة إيصالات أو رد المبلغ المتبقي`)
    } else {
      alert(`❌ خطأ في العهدة ${advanceId}!\n\nالإيصالات تتجاوز المبلغ المصروف\nالزيادة: ${Math.abs(remaining).toLocaleString('ar-EG')} ج.م`)
    }
  }

  const handleAddReceipt = (advanceId) => {
    if (!newReceipt.description || !newReceipt.amount) {
      alert('⚠️ يرجى ملء جميع البيانات المطلوبة')
      return
    }

    alert(`✅ تم إضافة الإيصال بنجاح!\n\nالوصف: ${newReceipt.description}\nالمبلغ: ${parseFloat(newReceipt.amount).toLocaleString('ar-EG')} ج.م\nالتاريخ: ${newReceipt.date}`)

    // إعادة تعيين النموذج
    setNewReceipt({
      description: '',
      amount: '',
      date: new Date().toISOString().split('T')[0],
      category: '',
      attachments: []
    })
  }

  const handleBulkSettlement = () => {
    const pendingAdvances = advances.filter(a => a.status === 'قيد التسوية' && a.autoSettlementEnabled)
    alert(`🤖 جاري تشغيل التسوية التلقائية...\n\nعدد العهد المؤهلة: ${pendingAdvances.length}\n\nسيتم معالجة العهد التي تحتوي على إيصالات كاملة تلقائياً`)
  }

  // تحليل العهد
  const getAdvanceAnalytics = () => {
    const total = advances.length
    const pending = advances.filter(a => a.status === 'قيد التسوية').length
    const settled = advances.filter(a => a.status === 'مسواة').length
    const overdue = advances.filter(a => a.status === 'متأخر').length
    const totalAmount = advances.reduce((sum, a) => sum + a.amount, 0)
    const settledAmount = advances.filter(a => a.status === 'مسواة').reduce((sum, a) => sum + a.amount, 0)
    const avgSettlementTime = 7 // أيام (محاكاة)

    return {
      total,
      pending,
      settled,
      overdue,
      totalAmount,
      settledAmount,
      avgSettlementTime,
      settlementRate: total > 0 ? (settled / total) * 100 : 0
    }
  }

  const analytics = getAdvanceAnalytics()

  const getStatusColor = (status) => {
    switch (status) {
      case 'مدفوع': return 'bg-green-100 text-green-800'
      case 'مسواة': return 'bg-blue-100 text-blue-800'
      case 'قيد التسوية': return 'bg-yellow-100 text-yellow-800'
      case 'قيد المراجعة': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                💵 إدارة العهد والمصروفات
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <EnhancedButton
                variant="primary"
                icon="➕"
                onClick={() => alert('🔄 جاري إضافة عهدة جديدة...')}
              >
                عهدة جديدة
              </EnhancedButton>
              <EnhancedButton
                variant="secondary"
                icon="🤖"
                onClick={handleBulkSettlement}
              >
                تسوية تلقائية
              </EnhancedButton>
              <EnhancedButton
                variant="success"
                icon="📊"
                onClick={() => alert('📊 جاري إنشاء تقرير المصروفات...')}
              >
                تقرير شامل
              </EnhancedButton>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">

              {/* التسوية التلقائية */}
              {activeTab === 'auto-settlement' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">🤖 نظام التسوية التلقائية</h2>

                  {/* إعدادات التسوية التلقائية */}
                  <EnhancedCard className="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">⚙️ إعدادات التسوية التلقائية</h3>
                        <p className="text-gray-600 mb-4">
                          تفعيل التسوية التلقائية للعهد التي تحتوي على إيصالات كاملة
                        </p>
                        <div className="flex items-center space-x-4">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={autoSettlement}
                              onChange={(e) => setAutoSettlement(e.target.checked)}
                              className="mr-2"
                            />
                            <span className="text-sm">تفعيل التسوية التلقائية</span>
                          </label>
                          <StatusBadge status={autoSettlement ? "success" : "neutral"}>
                            {autoSettlement ? "مفعل" : "معطل"}
                          </StatusBadge>
                        </div>
                      </div>
                      <div className="hidden md:block">
                        <div className="text-6xl opacity-20">🤖</div>
                      </div>
                    </div>
                  </EnhancedCard>

                  {/* إحصائيات التسوية */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <EnhancedCard className="text-center">
                      <div className="text-3xl mb-2">📊</div>
                      <div className="text-sm text-gray-500">معدل التسوية</div>
                      <div className="text-2xl font-bold text-blue-600">
                        {analytics.settlementRate.toFixed(1)}%
                      </div>
                      <ProgressBar value={analytics.settlementRate} variant="info" size="sm" className="mt-2" />
                    </EnhancedCard>

                    <EnhancedCard className="text-center">
                      <div className="text-3xl mb-2">⏱️</div>
                      <div className="text-sm text-gray-500">متوسط وقت التسوية</div>
                      <div className="text-2xl font-bold text-green-600">
                        {analytics.avgSettlementTime} أيام
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center">
                      <div className="text-3xl mb-2">💰</div>
                      <div className="text-sm text-gray-500">إجمالي العهد</div>
                      <div className="text-lg font-bold text-purple-600">
                        {analytics.totalAmount.toLocaleString('ar-EG')} ج.م
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center">
                      <div className="text-3xl mb-2">✅</div>
                      <div className="text-sm text-gray-500">العهد المسواة</div>
                      <div className="text-lg font-bold text-green-600">
                        {analytics.settledAmount.toLocaleString('ar-EG')} ج.م
                      </div>
                    </EnhancedCard>
                  </div>

                  {/* العهد المؤهلة للتسوية التلقائية */}
                  <EnhancedCard>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">🎯 العهد المؤهلة للتسوية التلقائية</h3>

                    <div className="space-y-4">
                      {advances.filter(a => a.autoSettlementEnabled && a.status !== 'مسواة').map((advance) => (
                        <div key={advance.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h4 className="font-medium text-gray-900">{advance.id} - {advance.employeeName}</h4>
                              <p className="text-sm text-gray-600">{advance.purpose}</p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <StatusBadge status={
                                advance.settlementPercentage === 100 ? "success" :
                                advance.settlementPercentage >= 70 ? "warning" : "error"
                              }>
                                {advance.settlementPercentage}% مكتمل
                              </StatusBadge>
                              <EnhancedButton
                                size="sm"
                                variant={advance.settlementPercentage === 100 ? "success" : "warning"}
                                onClick={() => handleAutoSettlement(advance.id)}
                              >
                                {advance.settlementPercentage === 100 ? "تسوية نهائية" : "مراجعة"}
                              </EnhancedButton>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="text-center">
                              <div className="text-sm text-gray-500">المبلغ الأصلي</div>
                              <div className="text-lg font-bold text-blue-600">
                                {advance.amount.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                            <div className="text-center">
                              <div className="text-sm text-gray-500">الإيصالات</div>
                              <div className="text-lg font-bold text-green-600">
                                {advance.totalReceipts.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                            <div className="text-center">
                              <div className="text-sm text-gray-500">المتبقي</div>
                              <div className={`text-lg font-bold ${
                                advance.remainingAmount === 0 ? 'text-green-600' :
                                advance.remainingAmount > 0 ? 'text-orange-600' : 'text-red-600'
                              }`}>
                                {advance.remainingAmount.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                          </div>

                          <div className="mt-3">
                            <ProgressBar
                              value={advance.settlementPercentage}
                              variant={
                                advance.settlementPercentage === 100 ? "success" :
                                advance.settlementPercentage >= 70 ? "warning" : "error"
                              }
                              showLabel
                              label={`نسبة التسوية: ${advance.settlementPercentage}%`}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </EnhancedCard>
                </div>
              )}

              {/* العهد */}
              {activeTab === 'advances' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">💰 إدارة العهد</h2>

                  {/* إحصائيات سريعة */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <EnhancedCard className="text-center bg-blue-50 border-blue-200">
                      <div className="text-3xl mb-2">📊</div>
                      <div className="text-blue-800 text-sm font-medium">إجمالي العهد</div>
                      <div className="text-blue-900 text-2xl font-bold">
                        {analytics.total}
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-yellow-50 border-yellow-200">
                      <div className="text-3xl mb-2">⏳</div>
                      <div className="text-yellow-800 text-sm font-medium">قيد التسوية</div>
                      <div className="text-yellow-900 text-2xl font-bold">
                        {analytics.pending}
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-green-50 border-green-200">
                      <div className="text-3xl mb-2">✅</div>
                      <div className="text-green-800 text-sm font-medium">مسواة</div>
                      <div className="text-green-900 text-2xl font-bold">
                        {analytics.settled}
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-red-50 border-red-200">
                      <div className="text-3xl mb-2">⚠️</div>
                      <div className="text-red-800 text-sm font-medium">متأخرة</div>
                      <div className="text-red-900 text-2xl font-bold">
                        {analytics.overdue}
                      </div>
                    </EnhancedCard>
                  </div>

                  {/* جدول العهد المحسن */}
                  <EnhancedCard>
                    <div className="space-y-4">
                      {advances.map((advance) => (
                        <div key={advance.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-4">
                              <div>
                                <h4 className="font-medium text-gray-900">{advance.id}</h4>
                                <p className="text-sm text-gray-600">{advance.employeeName}</p>
                              </div>
                              <StatusBadge status={
                                advance.status === 'مسواة' ? 'success' :
                                advance.status === 'قيد التسوية' ? 'warning' :
                                advance.status === 'متأخر' ? 'error' : 'info'
                              }>
                                {advance.status}
                              </StatusBadge>
                              {advance.priority === 'عالي' && (
                                <StatusBadge status="error">أولوية عالية</StatusBadge>
                              )}
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold text-gray-900">
                                {advance.amount.toLocaleString('ar-EG')} ج.م
                              </div>
                              <div className="text-sm text-gray-500">{advance.date}</div>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                            <div>
                              <div className="text-sm text-gray-500">القسم/المشروع</div>
                              <div className="font-medium">{advance.department}</div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-500">الغرض</div>
                              <div className="font-medium">{advance.purpose}</div>
                            </div>
                          </div>

                          {advance.status !== 'جديد' && (
                            <div className="mb-3">
                              <div className="flex justify-between text-sm mb-1">
                                <span>نسبة التسوية</span>
                                <span>{advance.settlementPercentage}%</span>
                              </div>
                              <ProgressBar
                                value={advance.settlementPercentage}
                                variant={
                                  advance.settlementPercentage === 100 ? "success" :
                                  advance.settlementPercentage >= 70 ? "warning" : "error"
                                }
                                size="sm"
                              />
                              <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>الإيصالات: {advance.totalReceipts.toLocaleString('ar-EG')} ج.م</span>
                                <span>المتبقي: {advance.remainingAmount.toLocaleString('ar-EG')} ج.م</span>
                              </div>
                            </div>
                          )}

                          <div className="flex justify-end space-x-2">
                            <EnhancedButton
                              size="sm"
                              variant="ghost"
                              onClick={() => setSelectedAdvance(advance)}
                            >
                              عرض التفاصيل
                            </EnhancedButton>
                            {advance.status === 'قيد التسوية' && (
                              <EnhancedButton
                                size="sm"
                                variant="warning"
                                onClick={() => handleAutoSettlement(advance.id)}
                              >
                                تسوية
                              </EnhancedButton>
                            )}
                            {advance.autoSettlementEnabled && (
                              <div className="flex items-center">
                                <span className="text-xs text-green-600">🤖 تسوية تلقائية</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </EnhancedCard>
                </div>
              )}

              {/* تحليلات العهد */}
              {activeTab === 'analytics' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📈 تحليلات العهد والأداء</h2>

                  {/* مؤشرات الأداء الرئيسية */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <EnhancedCard className="text-center bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                      <div className="text-4xl mb-3">📊</div>
                      <div className="text-sm text-blue-600 font-medium">معدل التسوية</div>
                      <div className="text-3xl font-bold text-blue-800 mb-2">
                        {analytics.settlementRate.toFixed(1)}%
                      </div>
                      <ProgressBar value={analytics.settlementRate} variant="info" size="sm" />
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                      <div className="text-4xl mb-3">⏱️</div>
                      <div className="text-sm text-green-600 font-medium">متوسط وقت التسوية</div>
                      <div className="text-3xl font-bold text-green-800">
                        {analytics.avgSettlementTime}
                      </div>
                      <div className="text-sm text-green-600">أيام</div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                      <div className="text-4xl mb-3">💰</div>
                      <div className="text-sm text-purple-600 font-medium">إجمالي العهد</div>
                      <div className="text-2xl font-bold text-purple-800">
                        {analytics.totalAmount.toLocaleString('ar-EG')}
                      </div>
                      <div className="text-sm text-purple-600">جنيه مصري</div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                      <div className="text-4xl mb-3">🎯</div>
                      <div className="text-sm text-orange-600 font-medium">كفاءة التسوية</div>
                      <div className="text-3xl font-bold text-orange-800">
                        {((analytics.settled / analytics.total) * 100).toFixed(0)}%
                      </div>
                      <div className="text-sm text-orange-600">من العهد</div>
                    </EnhancedCard>
                  </div>

                  {/* تحليل حسب الحالة */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <EnhancedCard>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">📊 توزيع العهد حسب الحالة</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">مسواة</span>
                          <div className="flex items-center space-x-2">
                            <ProgressBar value={(analytics.settled / analytics.total) * 100} variant="success" size="sm" className="w-24" />
                            <span className="text-sm font-medium">{analytics.settled}</span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">قيد التسوية</span>
                          <div className="flex items-center space-x-2">
                            <ProgressBar value={(analytics.pending / analytics.total) * 100} variant="warning" size="sm" className="w-24" />
                            <span className="text-sm font-medium">{analytics.pending}</span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">متأخرة</span>
                          <div className="flex items-center space-x-2">
                            <ProgressBar value={(analytics.overdue / analytics.total) * 100} variant="error" size="sm" className="w-24" />
                            <span className="text-sm font-medium">{analytics.overdue}</span>
                          </div>
                        </div>
                      </div>
                    </EnhancedCard>

                    <EnhancedCard>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">💵 التحليل المالي</h3>
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">إجمالي العهد</span>
                          <span className="font-medium">{analytics.totalAmount.toLocaleString('ar-EG')} ج.م</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">العهد المسواة</span>
                          <span className="font-medium text-green-600">{analytics.settledAmount.toLocaleString('ar-EG')} ج.م</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">العهد المعلقة</span>
                          <span className="font-medium text-orange-600">
                            {(analytics.totalAmount - analytics.settledAmount).toLocaleString('ar-EG')} ج.م
                          </span>
                        </div>
                        <div className="border-t pt-2">
                          <div className="flex justify-between">
                            <span className="text-sm font-medium text-gray-900">نسبة التسوية المالية</span>
                            <span className="font-bold text-blue-600">
                              {((analytics.settledAmount / analytics.totalAmount) * 100).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </EnhancedCard>
                  </div>

                  {/* تحليل الأداء الشهري */}
                  <EnhancedCard>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">📈 اتجاهات الأداء</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">+15%</div>
                        <div className="text-sm text-green-700">تحسن في سرعة التسوية</div>
                        <div className="text-xs text-gray-500">مقارنة بالشهر الماضي</div>
                      </div>
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">-2 أيام</div>
                        <div className="text-sm text-blue-700">تقليل متوسط وقت التسوية</div>
                        <div className="text-xs text-gray-500">من 9 إلى 7 أيام</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">95%</div>
                        <div className="text-sm text-purple-700">دقة التسوية التلقائية</div>
                        <div className="text-xs text-gray-500">معدل نجاح عالي</div>
                      </div>
                    </div>
                  </EnhancedCard>
                </div>
              )}

              {/* تسوية العهد */}
              {activeTab === 'settlement' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📋 تسوية العهد</h2>

                  <div className="space-y-6">
                    {advances.filter(a => a.status === 'قيد التسوية').map((advance) => (
                      <div key={advance.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h3 className="text-lg font-medium text-gray-900">
                              عهدة رقم: {advance.id}
                            </h3>
                            <p className="text-sm text-gray-600">
                              الموظف: {advance.employeeName} | المبلغ: {advance.amount.toLocaleString('ar-EG')} ج.م
                            </p>
                          </div>
                          <button
                            onClick={() => alert(`تسوية العهدة: ${advance.id}`)}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                          >
                            تسوية نهائية
                          </button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {/* الإيصالات المقدمة */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-3">الإيصالات المقدمة</h4>
                            <div className="space-y-2">
                              {advance.receipts.map((receipt) => (
                                <div key={receipt.id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                                  <div>
                                    <div className="text-sm font-medium">{receipt.description}</div>
                                    <div className="text-xs text-gray-500">{receipt.date}</div>
                                  </div>
                                  <div className="text-sm font-medium text-green-600">
                                    {receipt.amount.toLocaleString('ar-EG')} ج.م
                                  </div>
                                </div>
                              ))}
                            </div>

                            <div className="mt-4 p-3 bg-blue-50 rounded">
                              <div className="flex justify-between text-sm">
                                <span>إجمالي الإيصالات:</span>
                                <span className="font-medium">
                                  {advance.receipts.reduce((sum, r) => sum + r.amount, 0).toLocaleString('ar-EG')} ج.م
                                </span>
                              </div>
                              <div className="flex justify-between text-sm mt-1">
                                <span>المبلغ المتبقي:</span>
                                <span className="font-medium text-red-600">
                                  {(advance.amount - advance.receipts.reduce((sum, r) => sum + r.amount, 0)).toLocaleString('ar-EG')} ج.م
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* إضافة إيصال جديد */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-3">إضافة إيصال جديد</h4>
                            <div className="space-y-3">
                              <input
                                type="text"
                                placeholder="وصف الإيصال"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                              />
                              <input
                                type="number"
                                placeholder="المبلغ"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                              />
                              <input
                                type="date"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                              />
                              <button
                                onClick={() => alert('تم إضافة الإيصال')}
                                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                              >
                                إضافة إيصال
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* المصروفات العمومية */}
              {activeTab === 'general-expenses' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📊 المصروفات العمومية</h2>

                  {/* إحصائيات */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-red-50 p-4 rounded-lg">
                      <div className="text-red-800 text-sm font-medium">إجمالي المصروفات</div>
                      <div className="text-red-900 text-lg font-bold">
                        {generalExpenses.reduce((sum, e) => sum + e.amount, 0).toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-green-800 text-sm font-medium">المدفوع</div>
                      <div className="text-green-900 text-lg font-bold">
                        {generalExpenses
                          .filter(e => e.status === 'مدفوع')
                          .reduce((sum, e) => sum + e.amount, 0)
                          .toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-yellow-800 text-sm font-medium">قيد المراجعة</div>
                      <div className="text-yellow-900 text-lg font-bold">
                        {generalExpenses
                          .filter(e => e.status === 'قيد المراجعة')
                          .reduce((sum, e) => sum + e.amount, 0)
                          .toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-blue-800 text-sm font-medium">عدد المصروفات</div>
                      <div className="text-blue-900 text-2xl font-bold">
                        {generalExpenses.length}
                      </div>
                    </div>
                  </div>

                  {/* جدول المصروفات */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            رقم المصروف
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الفئة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الوصف
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            المبلغ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            التاريخ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            طريقة الدفع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            المشروع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الحالة
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {generalExpenses.map((expense) => (
                          <tr key={expense.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                              {expense.id}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {expense.category}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {expense.description}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {expense.amount.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {expense.date}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {expense.paymentMethod}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {expense.project}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(expense.status)}`}>
                                {expense.status}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* التقارير */}
              {activeTab === 'reports' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📈 تقارير العهد والمصروفات</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer">
                      <div className="text-center">
                        <div className="text-4xl mb-3">📊</div>
                        <h3 className="font-medium text-gray-900 mb-2">تقرير العهد الشهري</h3>
                        <p className="text-sm text-gray-600 mb-4">
                          تقرير شامل بجميع العهد المصروفة والمسواة خلال الشهر
                        </p>
                        <button
                          onClick={() => alert('جاري إنشاء تقرير العهد الشهري...')}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                        >
                          إنشاء التقرير
                        </button>
                      </div>
                    </div>

                    <div className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer">
                      <div className="text-center">
                        <div className="text-4xl mb-3">💰</div>
                        <h3 className="font-medium text-gray-900 mb-2">تقرير المصروفات العمومية</h3>
                        <p className="text-sm text-gray-600 mb-4">
                          تحليل المصروفات العمومية مقسمة حسب الفئات والمشاريع
                        </p>
                        <button
                          onClick={() => alert('جاري إنشاء تقرير المصروفات العمومية...')}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                        >
                          إنشاء التقرير
                        </button>
                      </div>
                    </div>

                    <div className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer">
                      <div className="text-center">
                        <div className="text-4xl mb-3">📋</div>
                        <h3 className="font-medium text-gray-900 mb-2">تقرير العهد المعلقة</h3>
                        <p className="text-sm text-gray-600 mb-4">
                          قائمة بجميع العهد التي لم يتم تسويتها بعد
                        </p>
                        <button
                          onClick={() => alert('جاري إنشاء تقرير العهد المعلقة...')}
                          className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                        >
                          إنشاء التقرير
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ExpenseManagement