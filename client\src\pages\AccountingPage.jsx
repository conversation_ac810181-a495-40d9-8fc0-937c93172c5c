import { useState } from 'react'

function AccountingPage() {
  const [activeTab, setActiveTab] = useState('chart-of-accounts')

  const handleLogout = () => {
    localStorage.removeItem('token')
    window.location.reload()
  }

  const chartOfAccounts = [
    { code: '1100', name: 'النقدية', type: 'أصول', balance: 150000 },
    { code: '1200', name: 'البنوك', type: 'أصول', balance: 250000 },
    { code: '1300', name: 'العملاء', type: 'أصول', balance: 180000 },
    { code: '1400', name: 'المخزون', type: 'أصول', balance: 120000 },
    { code: '2100', name: 'الموردون', type: 'خصوم', balance: 95000 },
    { code: '2200', name: 'مصروفات مستحقة', type: 'خصوم', balance: 45000 },
    { code: '3100', name: 'رأس المال', type: 'حقوق ملكية', balance: 500000 },
    { code: '4100', name: 'إيرادات المبيعات', type: 'إيرادات', balance: 750000 },
    { code: '5100', name: 'تكلفة البضاعة المباعة', type: 'مصروفات', balance: 450000 },
    { code: '5200', name: 'مصروفات عمومية', type: 'مصروفات', balance: 85000 }
  ]

  const journalEntries = [
    {
      id: 1,
      date: '2024-01-15',
      reference: 'JE-001',
      description: 'بيع مواد بناء للعميل أحمد علي',
      debit: { account: '1300 - العملاء', amount: 50000 },
      credit: { account: '4100 - إيرادات المبيعات', amount: 50000 }
    },
    {
      id: 2,
      date: '2024-01-16',
      reference: 'JE-002',
      description: 'شراء مواد بناء من المورد المتحدة',
      debit: { account: '1400 - المخزون', amount: 30000 },
      credit: { account: '2100 - الموردون', amount: 30000 }
    },
    {
      id: 3,
      date: '2024-01-17',
      reference: 'JE-003',
      description: 'دفع رواتب العمال',
      debit: { account: '5200 - مصروفات عمومية', amount: 25000 },
      credit: { account: '1100 - النقدية', amount: 25000 }
    }
  ]

  const tabs = [
    { id: 'chart-of-accounts', name: 'دليل الحسابات', icon: '📋', link: '/accounting/chart-of-accounts' },
    { id: 'general-ledger', name: 'دفتر الأستاذ العام', icon: '📚', link: '/accounting/general-ledger' },
    { id: 'trial-balance', name: 'ميزان المراجعة', icon: '⚖️', link: '/accounting/trial-balance' },
    { id: 'financial-statements', name: 'القوائم المالية', icon: '📊', link: '/accounting/financial-statements' },
    { id: 'journal-entries', name: 'القيود المحاسبية', icon: '📝' },
    { id: 'auto-posting', name: 'الترحيل التلقائي', icon: '🔄' }
  ]

  const getAccountTypeColor = (type) => {
    switch (type) {
      case 'أصول': return 'bg-green-100 text-green-800'
      case 'خصوم': return 'bg-red-100 text-red-800'
      case 'حقوق ملكية': return 'bg-blue-100 text-blue-800'
      case 'إيرادات': return 'bg-yellow-100 text-yellow-800'
      case 'مصروفات': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                📊 المحاسبة العامة
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => alert('🔄 جاري إنشاء قيد جديد...')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                ➕ قيد جديد
              </button>
              <button
                onClick={() => alert('📊 جاري إنشاء تقرير...')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📋 تقرير
              </button>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => {
                    if (tab.link) {
                      window.location.href = tab.link
                    } else {
                      setActiveTab(tab.id)
                    }
                  }}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{tab.icon}</span>
                  {tab.name}
                  {tab.link && <span className="mr-2 text-xs">🔗</span>}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              
              {/* Chart of Accounts */}
              {activeTab === 'chart-of-accounts' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📋 دليل الحسابات</h2>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            رمز الحساب
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            اسم الحساب
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            نوع الحساب
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الرصيد
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            إجراءات
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {chartOfAccounts.map((account, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {account.code}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {account.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getAccountTypeColor(account.type)}`}>
                                {account.type}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {account.balance.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <button 
                                onClick={() => alert(`عرض تفاصيل الحساب: ${account.name}`)}
                                className="text-blue-600 hover:text-blue-900 ml-4"
                              >
                                عرض
                              </button>
                              <button 
                                onClick={() => alert(`تعديل الحساب: ${account.name}`)}
                                className="text-green-600 hover:text-green-900"
                              >
                                تعديل
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Journal Entries */}
              {activeTab === 'journal-entries' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📝 القيود المحاسبية</h2>
                  <div className="space-y-4">
                    {journalEntries.map((entry) => (
                      <div key={entry.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h3 className="text-sm font-medium text-gray-900">
                              قيد رقم: {entry.reference}
                            </h3>
                            <p className="text-sm text-gray-500">
                              التاريخ: {entry.date}
                            </p>
                          </div>
                          <div className="flex space-x-2">
                            <button 
                              onClick={() => alert(`عرض تفاصيل القيد: ${entry.reference}`)}
                              className="text-blue-600 hover:text-blue-900 text-sm"
                            >
                              عرض
                            </button>
                            <button 
                              onClick={() => alert(`تعديل القيد: ${entry.reference}`)}
                              className="text-green-600 hover:text-green-900 text-sm"
                            >
                              تعديل
                            </button>
                          </div>
                        </div>
                        <p className="text-sm text-gray-700 mb-3">{entry.description}</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-green-50 p-3 rounded">
                            <h4 className="text-sm font-medium text-green-800 mb-1">مدين</h4>
                            <p className="text-sm text-green-700">{entry.debit.account}</p>
                            <p className="text-sm font-medium text-green-800">
                              {entry.debit.amount.toLocaleString('ar-EG')} ج.م
                            </p>
                          </div>
                          <div className="bg-red-50 p-3 rounded">
                            <h4 className="text-sm font-medium text-red-800 mb-1">دائن</h4>
                            <p className="text-sm text-red-700">{entry.credit.account}</p>
                            <p className="text-sm font-medium text-red-800">
                              {entry.credit.amount.toLocaleString('ar-EG')} ج.م
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Trial Balance */}
              {activeTab === 'trial-balance' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">⚖️ ميزان المراجعة</h2>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h3 className="text-sm font-medium text-blue-800 mb-2">ميزان المراجعة كما في 31/01/2024</h3>
                    <p className="text-sm text-blue-700">
                      يعرض هذا التقرير جميع الحسابات وأرصدتها للتأكد من توازن الدفاتر المحاسبية
                    </p>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            رمز الحساب
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            اسم الحساب
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            مدين
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            دائن
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {chartOfAccounts.map((account, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {account.code}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {account.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {['أصول', 'مصروفات'].includes(account.type) ? 
                                `${account.balance.toLocaleString('ar-EG')} ج.م` : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {['خصوم', 'حقوق ملكية', 'إيرادات'].includes(account.type) ? 
                                `${account.balance.toLocaleString('ar-EG')} ج.م` : '-'}
                            </td>
                          </tr>
                        ))}
                        <tr className="bg-gray-100 font-medium">
                          <td colSpan="2" className="px-6 py-4 text-sm text-gray-900">
                            الإجمالي
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            1,235,000 ج.م
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            1,235,000 ج.م
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Financial Statements */}
              {activeTab === 'financial-statements' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📊 القوائم المالية</h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Income Statement */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">قائمة الدخل</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">الإيرادات</span>
                          <span className="text-sm font-medium">750,000 ج.م</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">تكلفة البضاعة المباعة</span>
                          <span className="text-sm font-medium">(450,000) ج.م</span>
                        </div>
                        <div className="flex justify-between border-t pt-2">
                          <span className="text-sm font-medium">إجمالي الربح</span>
                          <span className="text-sm font-medium">300,000 ج.م</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">المصروفات العمومية</span>
                          <span className="text-sm font-medium">(85,000) ج.م</span>
                        </div>
                        <div className="flex justify-between border-t pt-2 font-medium">
                          <span className="text-sm">صافي الربح</span>
                          <span className="text-sm text-green-600">215,000 ج.م</span>
                        </div>
                      </div>
                    </div>

                    {/* Balance Sheet */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">الميزانية العمومية</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">الأصول</h4>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">النقدية والبنوك</span>
                              <span>400,000 ج.م</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">العملاء</span>
                              <span>180,000 ج.م</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">المخزون</span>
                              <span>120,000 ج.م</span>
                            </div>
                            <div className="flex justify-between border-t pt-1 font-medium">
                              <span>إجمالي الأصول</span>
                              <span>700,000 ج.م</span>
                            </div>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">الخصوم وحقوق الملكية</h4>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">الموردون</span>
                              <span>95,000 ج.م</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">مصروفات مستحقة</span>
                              <span>45,000 ج.م</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">رأس المال</span>
                              <span>500,000 ج.م</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">الأرباح المحتجزة</span>
                              <span>60,000 ج.م</span>
                            </div>
                            <div className="flex justify-between border-t pt-1 font-medium">
                              <span>إجمالي الخصوم وحقوق الملكية</span>
                              <span>700,000 ج.م</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AccountingPage
