import { useState } from 'react'
import { useQuery } from 'react-query'
import api from '../services/api'
import { 
  RevenueExpenseChart, 
  CashFlowChart, 
  ProjectPerformanceChart,
  ExpenseDistributionChart,
  KPIChart
} from '../components/charts/FinancialChart'
import { formatCurrency, formatNumber, formatPercentage } from '../utils/formatters'
import {
  ChartBarIcon,
  BriefcaseIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  BanknotesIcon,
  ClockIcon,
  CalendarIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline'

function EnhancedDashboard() {
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  })

  // البيانات المالية الأساسية
  const { data: financialSummary, isLoading: loadingFinancial } = useQuery(
    ['financial-summary', dateRange],
    () => api.get('/reports/financial-summary', { params: dateRange }).then(res => res.data.data),
    { refetchInterval: 300000 }
  )

  // الاتجاهات المالية
  const { data: trendsData } = useQuery(
    ['financial-trends', dateRange],
    () => api.get('/reports/financial-trends', { params: dateRange }).then(res => res.data.data),
    { refetchInterval: 300000 }
  )

  // أداء المشاريع
  const { data: projectsData } = useQuery(
    'project-performance',
    () => api.get('/reports/project-performance').then(res => res.data.data),
    { refetchInterval: 300000 }
  )

  // التدفق النقدي
  const { data: cashFlowData } = useQuery(
    ['cash-flow', dateRange],
    () => api.get('/reports/cash-flow', { params: dateRange }).then(res => res.data.data),
    { refetchInterval: 300000 }
  )

  // توزيع المصروفات
  const { data: expenseDistribution } = useQuery(
    ['expense-distribution', dateRange],
    () => api.get('/reports/expense-distribution', { params: dateRange }).then(res => res.data.data),
    { refetchInterval: 300000 }
  )

  // مؤشرات الأداء الرئيسية
  const { data: kpiData } = useQuery(
    'kpi-metrics',
    () => api.get('/reports/kpi-metrics').then(res => res.data.data),
    { refetchInterval: 300000 }
  )

  // التوصيات الذكية
  const { data: aiRecommendations } = useQuery(
    'ai-recommendations',
    () => api.get('/ai/recommendations').then(res => res.data.data),
    { refetchInterval: 600000 }
  )

  if (loadingFinancial) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="spinner h-8 w-8"></div>
      </div>
    )
  }

  const stats = [
    {
      name: 'إجمالي الإيرادات',
      value: formatCurrency(financialSummary?.totalRevenue || 0),
      change: financialSummary?.revenueChange || 0,
      icon: CurrencyDollarIcon,
      color: 'text-green-600'
    },
    {
      name: 'إجمالي المصروفات',
      value: formatCurrency(financialSummary?.totalExpenses || 0),
      change: financialSummary?.expenseChange || 0,
      icon: BanknotesIcon,
      color: 'text-red-600'
    },
    {
      name: 'صافي الربح',
      value: formatCurrency(financialSummary?.netProfit || 0),
      change: financialSummary?.profitChange || 0,
      icon: ChartBarIcon,
      color: 'text-blue-600'
    },
    {
      name: 'المشاريع النشطة',
      value: formatNumber(financialSummary?.activeProjects || 0),
      change: financialSummary?.projectsChange || 0,
      icon: BriefcaseIcon,
      color: 'text-purple-600'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">لوحة التحكم التفاعلية</h1>
          <p className="text-gray-600">نظرة شاملة على أداء الشركة</p>
        </div>
        
        {/* Date Range Selector */}
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="flex items-center space-x-2 space-x-reverse">
            <CalendarIcon className="h-5 w-5 text-gray-400" />
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="input input-sm"
            />
            <span className="text-gray-500">إلى</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="input input-sm"
            />
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className={`h-8 w-8 ${stat.color}`} />
              </div>
              <div className="mr-4 flex-1">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <div className="flex items-center mt-1">
                  {stat.change >= 0 ? (
                    <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <ArrowTrendingDownIcon className="h-4 w-4 text-red-500" />
                  )}
                  <span className={`text-sm mr-1 ${
                    stat.change >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formatPercentage(Math.abs(stat.change))}
                  </span>
                  <span className="text-sm text-gray-500">من الشهر الماضي</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue vs Expenses */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">الإيرادات مقابل المصروفات</h3>
          </div>
          <div className="card-content">
            <RevenueExpenseChart data={trendsData} height={300} />
          </div>
        </div>

        {/* Cash Flow */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">التدفق النقدي</h3>
          </div>
          <div className="card-content">
            <CashFlowChart data={cashFlowData} height={300} />
          </div>
        </div>

        {/* Project Performance */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">أداء المشاريع</h3>
          </div>
          <div className="card-content">
            <ProjectPerformanceChart data={projectsData} height={300} />
          </div>
        </div>

        {/* Expense Distribution */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">توزيع المصروفات</h3>
          </div>
          <div className="card-content">
            <ExpenseDistributionChart data={expenseDistribution} height={300} />
          </div>
        </div>
      </div>

      {/* KPI Metrics */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">مؤشرات الأداء الرئيسية</h3>
        </div>
        <div className="card-content">
          <KPIChart data={kpiData} height={250} />
        </div>
      </div>

      {/* AI Recommendations */}
      {aiRecommendations && aiRecommendations.length > 0 && (
        <div className="card">
          <div className="card-header">
            <h3 className="card-title flex items-center">
              <AdjustmentsHorizontalIcon className="h-5 w-5 ml-2" />
              التوصيات الذكية
            </h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {aiRecommendations.slice(0, 5).map((recommendation, index) => (
                <div key={index} className="flex items-start space-x-3 space-x-reverse p-4 bg-blue-50 rounded-lg">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-blue-900">
                      {recommendation.title}
                    </h4>
                    <p className="text-sm text-blue-700 mt-1">
                      {recommendation.description}
                    </p>
                    <div className="flex items-center mt-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        recommendation.priority === 'high' ? 'bg-red-100 text-red-800' :
                        recommendation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {recommendation.priority === 'high' ? 'عالي' :
                         recommendation.priority === 'medium' ? 'متوسط' : 'منخفض'}
                      </span>
                      <span className="text-xs text-gray-500 mr-2">
                        توفير محتمل: {formatCurrency(recommendation.potentialSavings || 0)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EnhancedDashboard
