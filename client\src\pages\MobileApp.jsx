import { useState, useEffect } from 'react'

function MobileApp() {
  const [installPrompt, setInstallPrompt] = useState(null)
  const [isInstalled, setIsInstalled] = useState(false)

  useEffect(() => {
    // Check if app is already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true)
    }

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault()
      setInstallPrompt(e)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    }
  }, [])

  const handleInstallApp = async () => {
    if (installPrompt) {
      installPrompt.prompt()
      const { outcome } = await installPrompt.userChoice
      if (outcome === 'accepted') {
        setIsInstalled(true)
        setInstallPrompt(null)
      }
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('token')
    window.location.reload()
  }

  const mobileFeatures = [
    {
      icon: '📱',
      title: 'تطبيق أصلي',
      description: 'تطبيق Progressive Web App يعمل كتطبيق أصلي',
      status: 'متاح'
    },
    {
      icon: '🔔',
      title: 'الإشعارات الفورية',
      description: 'تلقي إشعارات فورية للأحداث المهمة',
      status: 'متاح'
    },
    {
      icon: '📊',
      title: 'التقارير المحمولة',
      description: 'عرض التقارير والإحصائيات على الهاتف',
      status: 'متاح'
    },
    {
      icon: '💰',
      title: 'المعاملات السريعة',
      description: 'إدخال المعاملات المالية بسرعة',
      status: 'متاح'
    },
    {
      icon: '📷',
      title: 'مسح الفواتير',
      description: 'مسح وتحليل الفواتير بالكاميرا',
      status: 'قيد التطوير'
    },
    {
      icon: '🗺️',
      title: 'تتبع المواقع',
      description: 'تتبع مواقع المشاريع والفرق',
      status: 'قيد التطوير'
    },
    {
      icon: '🔄',
      title: 'المزامنة التلقائية',
      description: 'مزامنة البيانات تلقائياً مع الخادم',
      status: 'متاح'
    },
    {
      icon: '🔒',
      title: 'الأمان المتقدم',
      description: 'حماية بالبصمة ورقم PIN',
      status: 'مخطط'
    }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case 'متاح': return 'bg-green-100 text-green-800'
      case 'قيد التطوير': return 'bg-yellow-100 text-yellow-800'
      case 'مخطط': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                📱 تطبيق الهاتف المحمول
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {!isInstalled && installPrompt && (
                <button
                  onClick={handleInstallApp}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  📥 تثبيت التطبيق
                </button>
              )}
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* App Status */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="text-center">
            <div className="text-6xl mb-4">📱</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              نظام إدارة المقاولات - تطبيق الهاتف
            </h2>
            <p className="text-gray-600 mb-4">
              احصل على إمكانية الوصول الكامل للنظام من هاتفك المحمول
            </p>
            
            {isInstalled ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-center">
                  <svg className="h-5 w-5 text-green-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-green-800 font-medium">
                    ✅ التطبيق مثبت ويعمل بوضع التطبيق الأصلي
                  </span>
                </div>
              </div>
            ) : installPrompt ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 mb-3">
                  🎉 يمكنك تثبيت التطبيق على هاتفك للحصول على تجربة أفضل
                </p>
                <button
                  onClick={handleInstallApp}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium"
                >
                  📥 تثبيت التطبيق الآن
                </button>
              </div>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-yellow-800">
                  💡 يمكنك إضافة هذا الموقع إلى الشاشة الرئيسية لتجربة تطبيق أصلي
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Features */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6">🚀 ميزات التطبيق المحمول</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mobileFeatures.map((feature, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="text-center mb-3">
                  <div className="text-3xl mb-2">{feature.icon}</div>
                  <h4 className="font-medium text-gray-900">{feature.title}</h4>
                </div>
                <p className="text-sm text-gray-600 text-center mb-3">
                  {feature.description}
                </p>
                <div className="text-center">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(feature.status)}`}>
                    {feature.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Installation Instructions */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6">📋 تعليمات التثبيت</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Android Instructions */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">🤖 أندرويد (Chrome)</h4>
              <ol className="text-sm text-gray-600 space-y-2">
                <li>1. افتح الموقع في متصفح Chrome</li>
                <li>2. اضغط على قائمة المتصفح (⋮)</li>
                <li>3. اختر "إضافة إلى الشاشة الرئيسية"</li>
                <li>4. اضغط "إضافة" للتأكيد</li>
                <li>5. ستجد التطبيق في الشاشة الرئيسية</li>
              </ol>
            </div>

            {/* iOS Instructions */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">🍎 آيفون (Safari)</h4>
              <ol className="text-sm text-gray-600 space-y-2">
                <li>1. افتح الموقع في متصفح Safari</li>
                <li>2. اضغط على زر المشاركة (□↗)</li>
                <li>3. اختر "إضافة إلى الشاشة الرئيسية"</li>
                <li>4. اضغط "إضافة" للتأكيد</li>
                <li>5. ستجد التطبيق في الشاشة الرئيسية</li>
              </ol>
            </div>
          </div>
        </div>

        {/* App Benefits */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6">✨ مزايا التطبيق المحمول</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="text-2xl mr-3">⚡</div>
                <div>
                  <h4 className="font-medium text-gray-900">سرعة الوصول</h4>
                  <p className="text-sm text-gray-600">فتح سريع من الشاشة الرئيسية بدون متصفح</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="text-2xl mr-3">🔄</div>
                <div>
                  <h4 className="font-medium text-gray-900">العمل بدون إنترنت</h4>
                  <p className="text-sm text-gray-600">إمكانية العمل حتى بدون اتصال بالإنترنت</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="text-2xl mr-3">🔔</div>
                <div>
                  <h4 className="font-medium text-gray-900">الإشعارات الفورية</h4>
                  <p className="text-sm text-gray-600">تلقي إشعارات مهمة حتى عند إغلاق التطبيق</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="text-2xl mr-3">📱</div>
                <div>
                  <h4 className="font-medium text-gray-900">تجربة أصلية</h4>
                  <p className="text-sm text-gray-600">واجهة محسنة للهواتف المحمولة</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="text-2xl mr-3">🔒</div>
                <div>
                  <h4 className="font-medium text-gray-900">أمان محسن</h4>
                  <p className="text-sm text-gray-600">حماية إضافية للبيانات الحساسة</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="text-2xl mr-3">💾</div>
                <div>
                  <h4 className="font-medium text-gray-900">توفير البيانات</h4>
                  <p className="text-sm text-gray-600">استهلاك أقل للبيانات مع التخزين المؤقت</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MobileApp
