import { useQuery } from 'react-query'
import api from '../../services/api'

function HRPage() {
  const { data: employees, isLoading } = useQuery(
    'employees',
    () => api.get('/hr/employees').then(res => res.data.data)
  )

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount || 0)
  }

  if (isLoading) {
    return <div className="flex justify-center py-8"><div className="spinner h-8 w-8"></div></div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الموارد البشرية</h1>
          <p className="mt-1 text-sm text-gray-500">إدارة الموظفين والرواتب</p>
        </div>
        <button className="btn btn-primary">إضافة موظف جديد</button>
      </div>

      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>رمز الموظف</th>
                <th>الاسم</th>
                <th>الرقم القومي</th>
                <th>المسمى الوظيفي</th>
                <th>القسم</th>
                <th>الراتب الأساسي</th>
                <th>تاريخ التعيين</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {employees?.map((employee) => (
                <tr key={employee.id}>
                  <td className="font-mono">{employee.employee_code}</td>
                  <td>{employee.first_name} {employee.last_name}</td>
                  <td className="font-mono">{employee.national_id}</td>
                  <td>{employee.job_title}</td>
                  <td>{employee.department}</td>
                  <td>{formatCurrency(employee.basic_salary)}</td>
                  <td>{new Date(employee.hire_date).toLocaleDateString('ar-EG')}</td>
                  <td>
                    <span className={`badge ${employee.is_active ? 'badge-success' : 'badge-secondary'}`}>
                      {employee.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td>
                    <button className="btn btn-sm btn-outline ml-2">عرض</button>
                    <button className="btn btn-sm btn-secondary">تعديل</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default HRPage
