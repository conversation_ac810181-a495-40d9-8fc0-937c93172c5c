import { useState } from 'react'
import { useAuthStore } from '../store/authStore'

function SettingsPage() {
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('profile')

  const tabs = [
    { id: 'profile', name: 'الملف الشخصي', icon: '👤' },
    { id: 'company', name: 'بيانات الشركة', icon: '🏢' },
    { id: 'system', name: 'إعدادات النظام', icon: '⚙️' },
    { id: 'security', name: 'الأمان', icon: '🔒' }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">الإعدادات</h1>
        <p className="mt-1 text-sm text-gray-500">
          إدارة إعدادات النظام والملف الشخصي
        </p>
      </div>

      <div className="flex space-x-8 space-x-reverse">
        {/* Sidebar */}
        <div className="w-64">
          <nav className="space-y-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                  activeTab === tab.id
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <span className="ml-3 text-lg">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1">
          {activeTab === 'profile' && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">الملف الشخصي</h3>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="form-label">الاسم الأول</label>
                      <input
                        type="text"
                        defaultValue={user?.firstName}
                        className="form-input"
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="form-label">الاسم الأخير</label>
                      <input
                        type="text"
                        defaultValue={user?.lastName}
                        className="form-input"
                        readOnly
                      />
                    </div>
                  </div>
                  <div>
                    <label className="form-label">البريد الإلكتروني</label>
                    <input
                      type="email"
                      defaultValue={user?.email}
                      className="form-input"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="form-label">اسم المستخدم</label>
                    <input
                      type="text"
                      defaultValue={user?.username}
                      className="form-input"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="form-label">الدور</label>
                    <input
                      type="text"
                      defaultValue={user?.role}
                      className="form-input"
                      readOnly
                    />
                  </div>
                </div>
              </div>
              <div className="card-footer">
                <button className="btn btn-primary">حفظ التغييرات</button>
              </div>
            </div>
          )}

          {activeTab === 'company' && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">بيانات الشركة</h3>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  <div>
                    <label className="form-label">اسم الشركة</label>
                    <input type="text" className="form-input" placeholder="اسم الشركة" />
                  </div>
                  <div>
                    <label className="form-label">الرقم الضريبي</label>
                    <input type="text" className="form-input" placeholder="الرقم الضريبي" />
                  </div>
                  <div>
                    <label className="form-label">السجل التجاري</label>
                    <input type="text" className="form-input" placeholder="رقم السجل التجاري" />
                  </div>
                  <div>
                    <label className="form-label">العنوان</label>
                    <textarea className="form-input" rows="3" placeholder="عنوان الشركة"></textarea>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="form-label">الهاتف</label>
                      <input type="tel" className="form-input" placeholder="رقم الهاتف" />
                    </div>
                    <div>
                      <label className="form-label">البريد الإلكتروني</label>
                      <input type="email" className="form-input" placeholder="البريد الإلكتروني" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="card-footer">
                <button className="btn btn-primary">حفظ التغييرات</button>
              </div>
            </div>
          )}

          {activeTab === 'system' && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">إعدادات النظام</h3>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  <div>
                    <label className="form-label">العملة الأساسية</label>
                    <select className="form-input">
                      <option value="EGP">جنيه مصري (EGP)</option>
                      <option value="USD">دولار أمريكي (USD)</option>
                      <option value="EUR">يورو (EUR)</option>
                    </select>
                  </div>
                  <div>
                    <label className="form-label">بداية السنة المالية</label>
                    <input type="date" className="form-input" />
                  </div>
                  <div>
                    <label className="form-label">المنطقة الزمنية</label>
                    <select className="form-input">
                      <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                    </select>
                  </div>
                  <div>
                    <label className="form-label">اللغة</label>
                    <select className="form-input">
                      <option value="ar">العربية</option>
                      <option value="en">English</option>
                    </select>
                  </div>
                </div>
              </div>
              <div className="card-footer">
                <button className="btn btn-primary">حفظ التغييرات</button>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">تغيير كلمة المرور</h3>
                </div>
                <div className="card-body">
                  <div className="space-y-4">
                    <div>
                      <label className="form-label">كلمة المرور الحالية</label>
                      <input type="password" className="form-input" />
                    </div>
                    <div>
                      <label className="form-label">كلمة المرور الجديدة</label>
                      <input type="password" className="form-input" />
                    </div>
                    <div>
                      <label className="form-label">تأكيد كلمة المرور الجديدة</label>
                      <input type="password" className="form-input" />
                    </div>
                  </div>
                </div>
                <div className="card-footer">
                  <button className="btn btn-primary">تغيير كلمة المرور</button>
                </div>
              </div>

              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">إعدادات الأمان</h3>
                </div>
                <div className="card-body">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">المصادقة الثنائية</h4>
                        <p className="text-sm text-gray-500">تفعيل المصادقة الثنائية لحماية إضافية</p>
                      </div>
                      <button className="btn btn-outline">تفعيل</button>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">تسجيل الخروج التلقائي</h4>
                        <p className="text-sm text-gray-500">تسجيل الخروج بعد فترة عدم نشاط</p>
                      </div>
                      <select className="form-input w-32">
                        <option value="30">30 دقيقة</option>
                        <option value="60">ساعة واحدة</option>
                        <option value="120">ساعتان</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default SettingsPage
