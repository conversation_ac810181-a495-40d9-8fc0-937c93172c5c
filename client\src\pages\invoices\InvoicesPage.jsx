import { useQuery } from 'react-query'
import api from '../../services/api'

function InvoicesPage() {
  const { data: invoices, isLoading } = useQuery(
    'invoices',
    () => api.get('/invoices').then(res => res.data.data)
  )

  const getStatusBadge = (status) => {
    const statusMap = {
      draft: { label: 'مسودة', class: 'badge-secondary' },
      sent: { label: 'مرسلة', class: 'badge-info' },
      paid: { label: 'مدفوعة', class: 'badge-success' },
      overdue: { label: 'متأخرة', class: 'badge-danger' },
      cancelled: { label: 'ملغية', class: 'badge-secondary' }
    }
    const statusInfo = statusMap[status] || { label: status, class: 'badge-secondary' }
    return <span className={`badge ${statusInfo.class}`}>{statusInfo.label}</span>
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount || 0)
  }

  if (isLoading) {
    return <div className="flex justify-center py-8"><div className="spinner h-8 w-8"></div></div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الفواتير</h1>
          <p className="mt-1 text-sm text-gray-500">فواتير المبيعات والمشتريات</p>
        </div>
        <button className="btn btn-primary">إنشاء فاتورة جديدة</button>
      </div>

      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>رقم الفاتورة</th>
                <th>النوع</th>
                <th>الطرف</th>
                <th>تاريخ الفاتورة</th>
                <th>المبلغ الإجمالي</th>
                <th>المبلغ المدفوع</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {invoices?.map((invoice) => (
                <tr key={invoice.id}>
                  <td className="font-mono">{invoice.invoice_number}</td>
                  <td>
                    <span className={`badge ${invoice.invoice_type === 'sales' ? 'badge-success' : 'badge-info'}`}>
                      {invoice.invoice_type === 'sales' ? 'مبيعات' : 'مشتريات'}
                    </span>
                  </td>
                  <td>{invoice.party_name}</td>
                  <td>{new Date(invoice.invoice_date).toLocaleDateString('ar-EG')}</td>
                  <td>{formatCurrency(invoice.total_amount)}</td>
                  <td>{formatCurrency(invoice.paid_amount)}</td>
                  <td>{getStatusBadge(invoice.status)}</td>
                  <td>
                    <button className="btn btn-sm btn-outline ml-2">عرض</button>
                    <button className="btn btn-sm btn-secondary">طباعة</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default InvoicesPage
