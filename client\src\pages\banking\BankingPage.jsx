import { useQuery } from 'react-query'
import api from '../../services/api'

function BankingPage() {
  const { data: accounts, isLoading } = useQuery(
    'bank-accounts',
    () => api.get('/banking/accounts').then(res => res.data.data)
  )

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount || 0)
  }

  if (isLoading) {
    return <div className="flex justify-center py-8"><div className="spinner h-8 w-8"></div></div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الخزينة والبنوك</h1>
          <p className="mt-1 text-sm text-gray-500">إدارة الحسابات البنكية والخزينة</p>
        </div>
        <button className="btn btn-primary">إضافة حساب بنكي</button>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {accounts?.map((account) => (
          <div key={account.id} className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">{account.account_name}</h3>
              <span className={`badge ${account.is_active ? 'badge-success' : 'badge-secondary'}`}>
                {account.is_active ? 'نشط' : 'غير نشط'}
              </span>
            </div>
            
            <div className="card-body">
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">البنك</p>
                  <p className="text-sm text-gray-900">{account.bank_name}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-700">رقم الحساب</p>
                  <p className="text-sm text-gray-900 font-mono">{account.account_number}</p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700">نوع الحساب</p>
                  <p className="text-sm text-gray-900">
                    {account.account_type === 'checking' ? 'جاري' :
                     account.account_type === 'savings' ? 'توفير' :
                     account.account_type === 'credit' ? 'ائتمان' :
                     account.account_type}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700">الرصيد الحالي</p>
                  <p className={`text-lg font-semibold ${
                    account.current_balance >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formatCurrency(account.current_balance)}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700">العملة</p>
                  <p className="text-sm text-gray-900">{account.currency}</p>
                </div>
              </div>
            </div>

            <div className="card-footer">
              <div className="flex justify-between">
                <button className="btn btn-sm btn-outline">عرض الحركات</button>
                <button className="btn btn-sm btn-secondary">تعديل</button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {accounts && accounts.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">لا توجد حسابات بنكية</p>
        </div>
      )}
    </div>
  )
}

export default BankingPage
