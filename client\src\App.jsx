import { useState, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import SimpleLoginPage from './pages/auth/SimpleLoginPage'
import SimpleDashboard from './pages/SimpleDashboard'
import SystemSettings from './pages/SystemSettings'
import AdvancedFeatures from './pages/AdvancedFeatures'
import MobileApp from './pages/MobileApp'
import AccountingPage from './pages/AccountingPage'
import ChartOfAccounts from './pages/accounting/ChartOfAccounts'
import GeneralLedger from './pages/accounting/GeneralLedger'
import TrialBalance from './pages/accounting/TrialBalance'
import FinancialStatements from './pages/accounting/FinancialStatements'
import ProjectManagement from './pages/projects/ProjectManagement'
import ExpenseManagement from './pages/expenses/ExpenseManagement'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem('token')
    setIsAuthenticated(!!token)
    setIsLoading(false)
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <Routes>
        <Route path="/login" element={<SimpleLoginPage />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    )
  }

  return (
    <Routes>
      <Route path="/" element={<Navigate to="/dashboard" replace />} />
      <Route path="/dashboard" element={<SimpleDashboard />} />
      <Route path="/accounting" element={<AccountingPage />} />
      <Route path="/accounting/chart-of-accounts" element={<ChartOfAccounts />} />
      <Route path="/accounting/general-ledger" element={<GeneralLedger />} />
      <Route path="/accounting/trial-balance" element={<TrialBalance />} />
      <Route path="/accounting/financial-statements" element={<FinancialStatements />} />
      <Route path="/projects" element={<ProjectManagement />} />
      <Route path="/expenses" element={<ExpenseManagement />} />
      <Route path="/settings" element={<SystemSettings />} />
      <Route path="/advanced-features" element={<AdvancedFeatures />} />
      <Route path="/mobile-app" element={<MobileApp />} />
      <Route path="/login" element={<Navigate to="/dashboard" replace />} />
      <Route path="*" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  )
}

export default App
