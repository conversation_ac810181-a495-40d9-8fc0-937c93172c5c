import { useState, useEffect } from 'react'
import { <PERSON>hancedCard, EnhancedButton, StatusBadge, ProgressBar } from '../components/ui'

function SystemSettings() {
  const [activeTab, setActiveTab] = useState('company')

  const showToast = (message, type = 'success') => {
    console.log(`${type}: ${message}`)
    alert(message)
  }

  const [settings, setSettings] = useState({
    // Company Settings
    companyName: 'شركة المقاولات المتحدة',
    taxId: '*********',
    commercialRegister: 'CR-2024-001',
    address: 'القاهرة، مصر',
    phone: '0*********0',
    email: '<EMAIL>',
    website: 'www.construction.com',
    
    // System Settings
    language: 'ar',
    currency: 'EGP',
    dateFormat: 'dd/mm/yyyy',
    fiscalYearStart: '2024-01-01',
    timezone: 'Africa/Cairo',
    
    // Accounting Settings
    enableAutoPosting: true,
    enableTaxCalculation: true,
    defaultTaxRate: 14,
    enableInventoryTracking: true,
    enableProjectCosting: true,
    
    // Security Settings
    enableTwoFactor: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    enableAuditLog: true,
    
    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    systemAlerts: true,
    reportSchedule: 'weekly'
  })

  const handleLogout = () => {
    localStorage.removeItem('token')
    window.location.reload()
  }

  const handleSave = () => {
    localStorage.setItem('appSettings', JSON.stringify(settings))
    alert('تم حفظ الإعدادات بنجاح!')
  }

  const handleReset = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      localStorage.removeItem('appSettings')
      window.location.reload()
    }
  }

  const handleBackup = () => {
    const data = {
      settings: settings,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `construction-erp-backup-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    alert('تم إنشاء النسخة الاحتياطية بنجاح!')
  }

  useEffect(() => {
    const savedSettings = localStorage.getItem('appSettings')
    if (savedSettings) {
      setSettings({ ...settings, ...JSON.parse(savedSettings) })
    }
  }, [])

  const tabs = [
    { id: 'company', name: 'معلومات الشركة', icon: '🏢' },
    { id: 'system', name: 'إعدادات النظام', icon: '⚙️' },
    { id: 'accounting', name: 'إعدادات المحاسبة', icon: '📊' },
    { id: 'security', name: 'الأمان', icon: '🔒' },
    { id: 'notifications', name: 'الإشعارات', icon: '🔔' },
    { id: 'backup', name: 'النسخ الاحتياطي', icon: '💾' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                ⚙️ إعدادات النظام
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleSave}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                💾 حفظ الإعدادات
              </button>
              <button
                onClick={handleReset}
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                🔄 إعادة تعيين
              </button>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              
              {/* Company Settings */}
              {activeTab === 'company' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">🏢 معلومات الشركة</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم الشركة
                      </label>
                      <input
                        type="text"
                        value={settings.companyName}
                        onChange={(e) => setSettings({...settings, companyName: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        dir="rtl"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الرقم الضريبي
                      </label>
                      <input
                        type="text"
                        value={settings.taxId}
                        onChange={(e) => setSettings({...settings, taxId: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        dir="rtl"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        السجل التجاري
                      </label>
                      <input
                        type="text"
                        value={settings.commercialRegister}
                        onChange={(e) => setSettings({...settings, commercialRegister: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        dir="rtl"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف
                      </label>
                      <input
                        type="text"
                        value={settings.phone}
                        onChange={(e) => setSettings({...settings, phone: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        dir="rtl"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        العنوان
                      </label>
                      <textarea
                        value={settings.address}
                        onChange={(e) => setSettings({...settings, address: e.target.value})}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        dir="rtl"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني
                      </label>
                      <input
                        type="email"
                        value={settings.email}
                        onChange={(e) => setSettings({...settings, email: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الموقع الإلكتروني
                      </label>
                      <input
                        type="url"
                        value={settings.website}
                        onChange={(e) => setSettings({...settings, website: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* System Settings */}
              {activeTab === 'system' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">⚙️ إعدادات النظام</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اللغة
                      </label>
                      <select
                        value={settings.language}
                        onChange={(e) => setSettings({...settings, language: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        العملة
                      </label>
                      <select
                        value={settings.currency}
                        onChange={(e) => setSettings({...settings, currency: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="EGP">جنيه مصري (EGP)</option>
                        <option value="USD">دولار أمريكي (USD)</option>
                        <option value="EUR">يورو (EUR)</option>
                        <option value="SAR">ريال سعودي (SAR)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تنسيق التاريخ
                      </label>
                      <select
                        value={settings.dateFormat}
                        onChange={(e) => setSettings({...settings, dateFormat: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
                        <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                        <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        بداية السنة المالية
                      </label>
                      <input
                        type="date"
                        value={settings.fiscalYearStart}
                        onChange={(e) => setSettings({...settings, fiscalYearStart: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Accounting Settings */}
              {activeTab === 'accounting' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📊 إعدادات المحاسبة</h2>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">الترحيل التلقائي</h3>
                        <p className="text-sm text-gray-500">ترحيل القيود المحاسبية تلقائياً</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.enableAutoPosting}
                        onChange={(e) => setSettings({...settings, enableAutoPosting: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">حساب الضرائب</h3>
                        <p className="text-sm text-gray-500">تفعيل حساب الضرائب التلقائي</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.enableTaxCalculation}
                        onChange={(e) => setSettings({...settings, enableTaxCalculation: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        معدل الضريبة الافتراضي (%)
                      </label>
                      <input
                        type="number"
                        value={settings.defaultTaxRate}
                        onChange={(e) => setSettings({...settings, defaultTaxRate: parseFloat(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        max="100"
                        step="0.1"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">تتبع المخزون</h3>
                        <p className="text-sm text-gray-500">تفعيل نظام تتبع المخزون</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.enableInventoryTracking}
                        onChange={(e) => setSettings({...settings, enableInventoryTracking: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">تكلفة المشاريع</h3>
                        <p className="text-sm text-gray-500">تفعيل نظام تكلفة المشاريع</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.enableProjectCosting}
                        onChange={(e) => setSettings({...settings, enableProjectCosting: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Security Settings */}
              {activeTab === 'security' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">🔒 إعدادات الأمان</h2>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">المصادقة الثنائية</h3>
                        <p className="text-sm text-gray-500">تفعيل المصادقة الثنائية للحسابات</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.enableTwoFactor}
                        onChange={(e) => setSettings({...settings, enableTwoFactor: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        مهلة انتهاء الجلسة (دقيقة)
                      </label>
                      <input
                        type="number"
                        value={settings.sessionTimeout}
                        onChange={(e) => setSettings({...settings, sessionTimeout: parseInt(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="5"
                        max="480"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        انتهاء صلاحية كلمة المرور (يوم)
                      </label>
                      <input
                        type="number"
                        value={settings.passwordExpiry}
                        onChange={(e) => setSettings({...settings, passwordExpiry: parseInt(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="30"
                        max="365"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">سجل المراجعة</h3>
                        <p className="text-sm text-gray-500">تفعيل سجل مراجعة العمليات</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.enableAuditLog}
                        onChange={(e) => setSettings({...settings, enableAuditLog: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Notifications Settings */}
              {activeTab === 'notifications' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">🔔 إعدادات الإشعارات</h2>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">إشعارات البريد الإلكتروني</h3>
                        <p className="text-sm text-gray-500">استقبال الإشعارات عبر البريد الإلكتروني</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.emailNotifications}
                        onChange={(e) => setSettings({...settings, emailNotifications: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">إشعارات الرسائل النصية</h3>
                        <p className="text-sm text-gray-500">استقبال الإشعارات عبر الرسائل النصية</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.smsNotifications}
                        onChange={(e) => setSettings({...settings, smsNotifications: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">تنبيهات النظام</h3>
                        <p className="text-sm text-gray-500">عرض التنبيهات داخل النظام</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.systemAlerts}
                        onChange={(e) => setSettings({...settings, systemAlerts: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        جدولة التقارير
                      </label>
                      <select
                        value={settings.reportSchedule}
                        onChange={(e) => setSettings({...settings, reportSchedule: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="daily">يومي</option>
                        <option value="weekly">أسبوعي</option>
                        <option value="monthly">شهري</option>
                        <option value="quarterly">ربع سنوي</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Backup Settings */}
              {activeTab === 'backup' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">💾 النسخ الاحتياطي</h2>
                  <div className="space-y-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h3 className="text-sm font-medium text-blue-800 mb-2">معلومات النسخ الاحتياطي</h3>
                      <p className="text-sm text-blue-700">
                        يمكنك إنشاء نسخة احتياطية من جميع إعدادات النظام وتحميلها كملف JSON.
                        يمكن استخدام هذا الملف لاستعادة الإعدادات في المستقبل.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <button
                        onClick={handleBackup}
                        className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium"
                      >
                        📥 إنشاء نسخة احتياطية
                      </button>
                      <button
                        onClick={() => document.getElementById('restore-file').click()}
                        className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium"
                      >
                        📤 استعادة من نسخة احتياطية
                      </button>
                    </div>

                    <input
                      id="restore-file"
                      type="file"
                      accept=".json"
                      style={{ display: 'none' }}
                      onChange={(e) => {
                        const file = e.target.files[0]
                        if (file) {
                          const reader = new FileReader()
                          reader.onload = (event) => {
                            try {
                              const data = JSON.parse(event.target.result)
                              if (data.settings) {
                                setSettings(data.settings)
                                alert('تم استعادة الإعدادات بنجاح!')
                              }
                            } catch (error) {
                              alert('خطأ في قراءة الملف!')
                            }
                          }
                          reader.readAsText(file)
                        }
                      }}
                    />

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h3 className="text-sm font-medium text-yellow-800 mb-2">⚠️ تحذير</h3>
                      <p className="text-sm text-yellow-700">
                        استعادة النسخة الاحتياطية ستستبدل جميع الإعدادات الحالية.
                        تأكد من إنشاء نسخة احتياطية من الإعدادات الحالية قبل الاستعادة.
                      </p>
                    </div>
                  </div>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SystemSettings
