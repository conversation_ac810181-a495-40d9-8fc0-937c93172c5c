import { useState } from 'react'
import { exportToPDF, exportToExcel, exportToJSON, importFromExcel, generateSampleData } from '../utils/exportUtils'

function AdvancedFeatures() {
  const [activeCategory, setActiveCategory] = useState('mobile')

  const handleLogout = () => {
    localStorage.removeItem('token')
    window.location.reload()
  }

  const handleExportPDF = async () => {
    try {
      const sampleData = generateSampleData()
      const result = await exportToPDF(sampleData, 'تقرير-النظام')
      alert(result.message)
    } catch (error) {
      alert('خطأ في تصدير PDF')
    }
  }

  const handleExportExcel = async () => {
    try {
      const sampleData = generateSampleData()
      const result = await exportToExcel(sampleData, 'بيانات-النظام')
      alert(result.message)
    } catch (error) {
      alert('خطأ في تصدير Excel')
    }
  }

  const handleExportJSON = () => {
    try {
      const sampleData = generateSampleData()
      const result = exportToJSON(sampleData, 'بيانات-النظام')
      alert(result.message)
    } catch (error) {
      alert('خطأ في تصدير JSON')
    }
  }

  const handleImportExcel = () => {
    document.getElementById('excel-import').click()
  }

  const categories = [
    { id: 'mobile', name: 'تطبيق الهاتف', icon: '📱' },
    { id: 'banking', name: 'التكامل البنكي', icon: '🏦' },
    { id: 'government', name: 'الأنظمة الحكومية', icon: '🏛️' },
    { id: 'analytics', name: 'التحليلات المتقدمة', icon: '📊' },
    { id: 'workflow', name: 'نظام الموافقات', icon: '✅' },
    { id: 'reports', name: 'التقارير المتقدمة', icon: '📋' },
    { id: 'ui', name: 'واجهة المستخدم', icon: '🎨' },
    { id: 'backup', name: 'النسخ الاحتياطي', icon: '💾' },
    { id: 'advanced', name: 'الميزات المتقدمة', icon: '🚀' },
    { id: 'export', name: 'التصدير والاستيراد', icon: '📤' }
  ]

  const features = {
    mobile: [
      { name: 'تطبيق Android', status: 'قيد التطوير', description: 'تطبيق أندرويد أصلي للوصول السريع' },
      { name: 'تطبيق iOS', status: 'مخطط', description: 'تطبيق آيفون وآيباد' },
      { name: 'Progressive Web App', status: 'متاح', description: 'تطبيق ويب متقدم يعمل كتطبيق أصلي' },
      { name: 'الإشعارات الفورية', status: 'متاح', description: 'إشعارات فورية للأحداث المهمة' }
    ],
    banking: [
      { name: 'البنك الأهلي المصري', status: 'متاح', description: 'ربط مباشر مع البنك الأهلي' },
      { name: 'بنك مصر', status: 'قيد التطوير', description: 'تكامل مع بنك مصر' },
      { name: 'البنك التجاري الدولي', status: 'مخطط', description: 'ربط مع CIB' },
      { name: 'مطابقة الحسابات التلقائية', status: 'متاح', description: 'مطابقة تلقائية للمعاملات البنكية' }
    ],
    government: [
      { name: 'مصلحة الضرائب المصرية', status: 'متاح', description: 'تصدير تلقائي للإقرارات الضريبية' },
      { name: 'الهيئة العامة للرقابة المالية', status: 'قيد التطوير', description: 'تقارير الامتثال المالي' },
      { name: 'وزارة القوى العاملة', status: 'مخطط', description: 'تقارير التأمينات الاجتماعية' },
      { name: 'الجهاز المركزي للإحصاء', status: 'مخطط', description: 'البيانات الإحصائية الاقتصادية' }
    ],
    analytics: [
      { name: 'التنبؤ بالتدفق النقدي', status: 'متاح', description: 'توقعات ذكية للتدفق النقدي' },
      { name: 'تحليل الربحية', status: 'متاح', description: 'تحليل مفصل لربحية المشاريع' },
      { name: 'مؤشرات الأداء الرئيسية', status: 'قيد التطوير', description: 'لوحة مؤشرات شاملة' },
      { name: 'التحليل التنبؤي', status: 'مخطط', description: 'تحليلات متقدمة بالذكاء الاصطناعي' }
    ],
    workflow: [
      { name: 'موافقات المشتريات', status: 'متاح', description: 'سير عمل موافقات المشتريات' },
      { name: 'موافقات المصروفات', status: 'متاح', description: 'نظام موافقة المصروفات' },
      { name: 'موافقات المشاريع', status: 'قيد التطوير', description: 'سير عمل إدارة المشاريع' },
      { name: 'الموافقات المخصصة', status: 'مخطط', description: 'إنشاء سير عمل مخصص' }
    ],
    reports: [
      { name: 'تقارير مالية متقدمة', status: 'متاح', description: 'تقارير مالية شاملة ومفصلة' },
      { name: 'تقارير المشاريع', status: 'متاح', description: 'تقارير تفصيلية للمشاريع' },
      { name: 'تقارير الضرائب', status: 'قيد التطوير', description: 'تقارير ضريبية متخصصة' },
      { name: 'التقارير المخصصة', status: 'مخطط', description: 'إنشاء تقارير حسب الطلب' }
    ],
    ui: [
      { name: 'الوضع الليلي', status: 'قيد التطوير', description: 'واجهة مظلمة لراحة العين' },
      { name: 'التخصيص المتقدم', status: 'مخطط', description: 'تخصيص كامل للواجهة' },
      { name: 'الاختصارات السريعة', status: 'متاح', description: 'اختصارات لوحة المفاتيح' },
      { name: 'الواجهة التفاعلية', status: 'متاح', description: 'تفاعل محسن مع المستخدم' }
    ],
    backup: [
      { name: 'النسخ التلقائي اليومي', status: 'متاح', description: 'نسخ احتياطي تلقائي يومي' },
      { name: 'التخزين السحابي', status: 'قيد التطوير', description: 'نسخ احتياطي في السحابة' },
      { name: 'الاستعادة السريعة', status: 'متاح', description: 'استعادة سريعة للبيانات' },
      { name: 'التشفير المتقدم', status: 'مخطط', description: 'تشفير عالي الأمان للنسخ' }
    ],
    advanced: [
      { name: 'دعم اللغات المتعددة', status: 'قيد التطوير', description: 'دعم العربية والإنجليزية والفرنسية' },
      { name: 'التحكم في الميزانية', status: 'متاح', description: 'إدارة متقدمة للميزانيات' },
      { name: 'تكامل CAD', status: 'مخطط', description: 'ربط مع برامج التصميم الهندسي' },
      { name: 'العقود الذكية', status: 'مخطط', description: 'استخدام تقنية البلوك تشين' },
      { name: 'السحابة المتعددة', status: 'قيد التطوير', description: 'دعم مزودي خدمة متعددين' },
      { name: 'إدارة الجودة', status: 'مخطط', description: 'نظام شامل لإدارة الجودة' },
      { name: 'إدارة علاقات العملاء', status: 'قيد التطوير', description: 'نظام CRM متكامل' },
      { name: 'إدارة الموردين', status: 'مخطط', description: 'نظام SRM متقدم' },
      { name: 'الواقع المعزز', status: 'مخطط', description: 'تقنيات AR للمواقع' },
      { name: 'الواجهة الصوتية', status: 'مخطط', description: 'التحكم بالأوامر الصوتية' }
    ],
    export: [
      { name: 'تصدير PDF', status: 'متاح', description: 'تصدير التقارير والفواتير إلى PDF' },
      { name: 'تصدير Excel', status: 'متاح', description: 'تصدير البيانات إلى ملفات Excel' },
      { name: 'استيراد Excel', status: 'متاح', description: 'استيراد البيانات من ملفات Excel' },
      { name: 'تصدير Word', status: 'قيد التطوير', description: 'تصدير التقارير إلى مستندات Word' },
      { name: 'استيراد CSV', status: 'متاح', description: 'استيراد البيانات من ملفات CSV' },
      { name: 'تصدير JSON', status: 'متاح', description: 'تصدير البيانات بصيغة JSON' }
    ]
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'متاح': return 'bg-green-100 text-green-800'
      case 'قيد التطوير': return 'bg-yellow-100 text-yellow-800'
      case 'مخطط': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                🚀 الميزات والإضافات المتقدمة
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleExportPDF}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📄 تصدير PDF
              </button>
              <button
                onClick={handleExportExcel}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📊 تصدير Excel
              </button>
              <button
                onClick={handleImportExcel}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📥 استيراد Excel
              </button>
              <button
                onClick={handleExportJSON}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📋 تصدير JSON
              </button>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeCategory === category.id
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">
                {categories.find(c => c.id === activeCategory)?.icon} {categories.find(c => c.id === activeCategory)?.name}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {features[activeCategory]?.map((feature, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-medium text-gray-900">{feature.name}</h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(feature.status)}`}>
                        {feature.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500">{feature.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hidden file input for Excel import */}
      <input
        id="excel-import"
        type="file"
        accept=".xlsx,.xls,.csv"
        style={{ display: 'none' }}
        onChange={async (e) => {
          const file = e.target.files[0]
          if (file) {
            try {
              alert(`🔄 جاري استيراد الملف: ${file.name}...`)
              const result = await importFromExcel(file)
              alert(result.message)
              console.log('Imported data:', result.data)
            } catch (error) {
              alert(error.message || 'خطأ في استيراد الملف')
            }
          }
        }}
      />
    </div>
  )
}

export default AdvancedFeatures
