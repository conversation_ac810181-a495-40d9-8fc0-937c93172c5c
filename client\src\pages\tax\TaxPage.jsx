import { useState } from 'react'
import { useQuery } from 'react-query'
import api from '../../services/api'

function TaxPage() {
  const [reportPeriod, setReportPeriod] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  })

  const { data: taxSettings, isLoading: loadingSettings } = useQuery(
    'tax-settings',
    () => api.get('/tax/settings').then(res => res.data.data)
  )

  const { data: vatReport, isLoading: loadingVAT } = useQuery(
    ['vat-report', reportPeriod],
    () => api.get('/tax/vat-report', { params: reportPeriod }).then(res => res.data.data),
    { enabled: !!reportPeriod.startDate && !!reportPeriod.endDate }
  )

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount || 0)
  }

  const formatPercentage = (rate) => {
    return `${(rate * 100).toFixed(2)}%`
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">الضرائب والامتثال</h1>
        <p className="mt-1 text-sm text-gray-500">
          إدارة الضرائب المصرية والامتثال الضريبي
        </p>
      </div>

      {/* Tax Settings */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">إعدادات الضرائب</h3>
        </div>
        <div className="card-body">
          {loadingSettings ? (
            <div className="flex justify-center py-4">
              <div className="spinner h-6 w-6"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              {taxSettings?.map((setting) => (
                <div key={setting.id} className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900">
                    {setting.tax_type === 'vat' ? 'ضريبة القيمة المضافة' :
                     setting.tax_type === 'withholding' ? 'ضريبة الخصم والإضافة' :
                     setting.tax_type === 'income_tax' ? 'ضريبة الدخل' :
                     setting.tax_type}
                  </h4>
                  <p className="text-2xl font-bold text-primary-600">
                    {formatPercentage(setting.tax_rate)}
                  </p>
                  <p className="text-sm text-gray-500">
                    ساري من: {new Date(setting.effective_date).toLocaleDateString('ar-EG')}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* VAT Report */}
      <div className="card">
        <div className="card-header">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">تقرير ضريبة القيمة المضافة</h3>
            <div className="flex space-x-4 space-x-reverse">
              <input
                type="date"
                value={reportPeriod.startDate}
                onChange={(e) => setReportPeriod(prev => ({ ...prev, startDate: e.target.value }))}
                className="form-input w-40"
              />
              <input
                type="date"
                value={reportPeriod.endDate}
                onChange={(e) => setReportPeriod(prev => ({ ...prev, endDate: e.target.value }))}
                className="form-input w-40"
              />
            </div>
          </div>
        </div>
        <div className="card-body">
          {loadingVAT ? (
            <div className="flex justify-center py-4">
              <div className="spinner h-6 w-6"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>التاريخ</th>
                    <th>النوع</th>
                    <th>المبلغ الخاضع للضريبة</th>
                    <th>معدل الضريبة</th>
                    <th>مبلغ الضريبة</th>
                    <th>الرقم الضريبي</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {vatReport?.map((transaction) => (
                    <tr key={transaction.id}>
                      <td>{new Date(transaction.transaction_date).toLocaleDateString('ar-EG')}</td>
                      <td>
                        <span className={`badge ${transaction.transaction_type === 'sales' ? 'badge-success' : 'badge-info'}`}>
                          {transaction.transaction_type === 'sales' ? 'مبيعات' : 'مشتريات'}
                        </span>
                      </td>
                      <td>{formatCurrency(transaction.taxable_amount)}</td>
                      <td>{formatPercentage(transaction.vat_rate)}</td>
                      <td>{formatCurrency(transaction.vat_amount)}</td>
                      <td className="font-mono">
                        {transaction.customer_tax_id || transaction.supplier_tax_id || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">إجراءات سريعة</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
            <button className="btn btn-outline">
              إنشاء نموذج 41
            </button>
            <button className="btn btn-outline">
              تقرير ضريبة الخصم
            </button>
            <button className="btn btn-outline">
              تقرير ضريبة الدخل
            </button>
            <button className="btn btn-outline">
              تصدير التقارير
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TaxPage
