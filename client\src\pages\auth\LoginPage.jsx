import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { useAuthStore } from '../../store/authStore'
import toast from 'react-hot-toast'

function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)
  const { login } = useAuthStore()
  
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm()

  const onSubmit = async (data) => {
    setIsLoading(true)
    try {
      const result = await login(data)
      if (result.success) {
        toast.success('تم تسجيل الدخول بنجاح')
      } else {
        toast.error(result.message || 'فشل في تسجيل الدخول')
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء تسجيل الدخول')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8" dir="rtl">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            نظام إدارة المقاولات
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            نظام محاسبي شامل مع الامتثال الضريبي المصري والذكاء الاصطناعي
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="rounded-md shadow-sm space-y-4">
            <div>
              <label htmlFor="username" className="form-label">
                اسم المستخدم أو البريد الإلكتروني
              </label>
              <input
                {...register('username', { 
                  required: 'اسم المستخدم مطلوب' 
                })}
                type="text"
                className="form-input"
                placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
              />
              {errors.username && (
                <p className="form-error">{errors.username.message}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="password" className="form-label">
                كلمة المرور
              </label>
              <input
                {...register('password', { 
                  required: 'كلمة المرور مطلوبة',
                  minLength: {
                    value: 6,
                    message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                  }
                })}
                type="password"
                className="form-input"
                placeholder="أدخل كلمة المرور"
              />
              {errors.password && (
                <p className="form-error">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="spinner h-5 w-5"></div>
              ) : (
                'تسجيل الدخول'
              )}
            </button>
          </div>

          <div className="text-center">
            <div className="text-sm text-gray-600">
              بيانات تجريبية:
            </div>
            <div className="text-xs text-gray-500 mt-1">
              المستخدم: admin | كلمة المرور: admin123
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default LoginPage
