import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './store/authStore'
import Layout from './components/Layout'
import LoginPage from './pages/auth/LoginPage'
import DashboardPage from './pages/DashboardPage'
import AccountingPage from './pages/accounting/AccountingPage'
import ProjectsPage from './pages/projects/ProjectsPage'
import InventoryPage from './pages/inventory/InventoryPage'
import InvoicesPage from './pages/invoices/InvoicesPage'
import PaymentsPage from './pages/payments/PaymentsPage'
import BankingPage from './pages/banking/BankingPage'
import HRPage from './pages/hr/HRPage'
import TaxPage from './pages/tax/TaxPage'
import ReportsPage from './pages/reports/ReportsPage'
import AIAnalyticsPage from './pages/ai/AIAnalyticsPage'
import SettingsPage from './pages/SettingsPage'

function App() {
  const { isAuthenticated, isLoading } = useAuthStore()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner h-8 w-8"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    )
  }

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<DashboardPage />} />
        <Route path="/accounting/*" element={<AccountingPage />} />
        <Route path="/projects/*" element={<ProjectsPage />} />
        <Route path="/inventory/*" element={<InventoryPage />} />
        <Route path="/invoices/*" element={<InvoicesPage />} />
        <Route path="/payments/*" element={<PaymentsPage />} />
        <Route path="/banking/*" element={<BankingPage />} />
        <Route path="/hr/*" element={<HRPage />} />
        <Route path="/tax/*" element={<TaxPage />} />
        <Route path="/reports/*" element={<ReportsPage />} />
        <Route path="/ai-analytics/*" element={<AIAnalyticsPage />} />
        <Route path="/settings" element={<SettingsPage />} />
        <Route path="/login" element={<Navigate to="/dashboard" replace />} />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Layout>
  )
}

export default App
